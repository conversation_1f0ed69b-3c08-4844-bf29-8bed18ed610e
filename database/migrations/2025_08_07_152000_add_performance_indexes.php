<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations for performance optimization.
     * Adds indexes to frequently queried columns for better performance.
     */
    public function up()
    {
        // Orders table indexes
        Schema::table('orders', function (Blueprint $table) {
            $table->index('status', 'idx_orders_status');
            $table->index('user_id', 'idx_orders_user_id');
            $table->index('delegate_id', 'idx_orders_delegate_id');
            $table->index('created_at', 'idx_orders_created_at');
            $table->index(['status', 'created_at'], 'idx_orders_status_created');
            $table->index(['user_id', 'status'], 'idx_orders_user_status');
        });

        // Users table indexes (check if columns exist first)
        Schema::table('users', function (Blueprint $table) {
            // Only add indexes for columns that exist
            if (Schema::hasColumn('users', 'user_type')) {
                $table->index('user_type', 'idx_users_user_type');
            }
            if (Schema::hasColumn('users', 'is_active')) {
                $table->index('is_active', 'idx_users_is_active');
            }
            if (Schema::hasColumn('users', 'is_blocked')) {
                $table->index('is_blocked', 'idx_users_is_blocked');
            }
            $table->index('created_at', 'idx_users_created_at');

            // Composite indexes only if both columns exist
            if (Schema::hasColumn('users', 'user_type') && Schema::hasColumn('users', 'is_active')) {
                $table->index(['user_type', 'is_active'], 'idx_users_type_active');
            }
            if (Schema::hasColumn('users', 'user_type')) {
                $table->index(['user_type', 'created_at'], 'idx_users_type_created');
            }
        });

        // User packages table indexes
        Schema::table('user_packages', function (Blueprint $table) {
            $table->index('user_id', 'idx_user_packages_user_id');
            $table->index('package_id', 'idx_user_packages_package_id');
            $table->index('status', 'idx_user_packages_status');
            $table->index('created_at', 'idx_user_packages_created_at');
            $table->index(['package_id', 'status'], 'idx_user_packages_pkg_status');
            $table->index(['user_id', 'status'], 'idx_user_packages_user_status');
        });

        // Packages table indexes
        Schema::table('packages', function (Blueprint $table) {
            $table->index('is_active', 'idx_packages_is_active');
            $table->index('created_at', 'idx_packages_created_at');
            $table->index(['is_active', 'created_at'], 'idx_packages_active_created');
        });

        // Addresses table indexes (if exists)
        if (Schema::hasTable('addresses')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->index('user_id', 'idx_addresses_user_id');
                $table->index('created_at', 'idx_addresses_created_at');
            });
        }

        // Transactions table indexes (if exists)
        if (Schema::hasTable('transactions')) {
            Schema::table('transactions', function (Blueprint $table) {
                $table->index('user_id', 'idx_transactions_user_id');
                $table->index('status', 'idx_transactions_status');
                $table->index('created_at', 'idx_transactions_created_at');
                $table->index(['user_id', 'status'], 'idx_transactions_user_status');
            });
        }

        // Bookings table indexes (if exists)
        if (Schema::hasTable('bookings')) {
            Schema::table('bookings', function (Blueprint $table) {
                $table->index('user_id', 'idx_bookings_user_id');
                $table->index('status', 'idx_bookings_status');
                $table->index('created_at', 'idx_bookings_created_at');
                $table->index(['user_id', 'status'], 'idx_bookings_user_status');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down()
    {
        // Orders table indexes
        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_status');
            $table->dropIndex('idx_orders_user_id');
            $table->dropIndex('idx_orders_delegate_id');
            $table->dropIndex('idx_orders_created_at');
            $table->dropIndex('idx_orders_status_created');
            $table->dropIndex('idx_orders_user_status');
        });

        // Users table indexes
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex('idx_users_user_type');
            $table->dropIndex('idx_users_is_active');
            $table->dropIndex('idx_users_is_blocked');
            $table->dropIndex('idx_users_created_at');
            $table->dropIndex('idx_users_type_active');
            $table->dropIndex('idx_users_type_created');
        });

        // User packages table indexes
        Schema::table('user_packages', function (Blueprint $table) {
            $table->dropIndex('idx_user_packages_user_id');
            $table->dropIndex('idx_user_packages_package_id');
            $table->dropIndex('idx_user_packages_status');
            $table->dropIndex('idx_user_packages_created_at');
            $table->dropIndex('idx_user_packages_pkg_status');
            $table->dropIndex('idx_user_packages_user_status');
        });

        // Packages table indexes
        Schema::table('packages', function (Blueprint $table) {
            $table->dropIndex('idx_packages_is_active');
            $table->dropIndex('idx_packages_created_at');
            $table->dropIndex('idx_packages_active_created');
        });

        // Addresses table indexes
        if (Schema::hasTable('addresses')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->dropIndex('idx_addresses_user_id');
                $table->dropIndex('idx_addresses_created_at');
            });
        }

        // Transactions table indexes
        if (Schema::hasTable('transactions')) {
            Schema::table('transactions', function (Blueprint $table) {
                $table->dropIndex('idx_transactions_user_id');
                $table->dropIndex('idx_transactions_status');
                $table->dropIndex('idx_transactions_created_at');
                $table->dropIndex('idx_transactions_user_status');
            });
        }

        // Bookings table indexes
        if (Schema::hasTable('bookings')) {
            Schema::table('bookings', function (Blueprint $table) {
                $table->dropIndex('idx_bookings_user_id');
                $table->dropIndex('idx_bookings_status');
                $table->dropIndex('idx_bookings_created_at');
                $table->dropIndex('idx_bookings_user_status');
            });
        }
    }
};
