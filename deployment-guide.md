# 🚀 QatraWahda Deployment Guide

## Overview
Deploy localhost improvements to VPS while preserving production data.

## 📋 Pre-Deployment Checklist

### 1. **Backup Production Database**
```bash
# On your VPS, create a backup
mysqldump -u username -p database_name > backup_$(date +%Y%m%d_%H%M%S).sql

# Download backup to local machine
scp user@your-vps:/path/to/backup.sql ./production_backup.sql
```

### 2. **Backup Production Files**
```bash
# Create archive of current production files
tar -czf production_backup_$(date +%Y%m%d_%H%M%S).tar.gz /path/to/your/project
```

## 🔄 Deployment Steps

### Step 1: Upload Code Changes
```bash
# Upload your improved code to VPS
rsync -avz --exclude 'node_modules' --exclude '.git' --exclude 'storage/logs' \
  /path/to/local/project/ user@your-vps:/path/to/production/

# Or using SCP
scp -r ./app ./resources ./routes ./config user@your-vps:/path/to/production/
```

### Step 2: Update Dependencies
```bash
# On VPS
cd /path/to/production
composer install --optimize-autoloader --no-dev
npm install && npm run production
```

### Step 3: Database Migration Strategy
Since you have the live database as `main.sql`, we'll use it as the base and apply any necessary schema updates.

#### Option A: Use Live Database + Apply Schema Changes
```bash
# 1. Import live database
mysql -u username -p database_name < main.sql

# 2. Run any new migrations (if any)
php artisan migrate --force
```

#### Option B: Selective Data Import
If you need to preserve some local test data:
```bash
# Import specific tables from live database
mysql -u username -p database_name < live_users_only.sql
```

### Step 4: Configuration Updates
```bash
# Update environment configuration
cp .env.production .env
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

### Step 5: File Permissions
```bash
# Set proper permissions
chown -R www-data:www-data /path/to/production
chmod -R 755 /path/to/production
chmod -R 775 /path/to/production/storage
chmod -R 775 /path/to/production/bootstrap/cache
```

## 🔧 Key Files to Update

### 1. **Environment Configuration (.env)**
```env
APP_ENV=production
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=localhost
DB_PORT=3306
DB_DATABASE=your_production_db
DB_USERNAME=your_db_user
DB_PASSWORD=your_db_password
```

### 2. **Updated Files from Localhost**
- `app/Http/Controllers/Dashboard/PackageController.php` ✅
- `resources/views/dashboard/packages/index.blade.php` ✅
- `resources/views/dashboard/packages/users.blade.php` ✅
- `resources/views/dashboard/packages/overview.blade.php` ✅
- `lang/en/dashboard.php` ✅
- `lang/ar/dashboard.php` ✅
- `routes/web.php` ✅

## 🧪 Testing Checklist

### After Deployment:
- [ ] Website loads correctly
- [ ] Dashboard login works
- [ ] Package management displays correctly
- [ ] Localization works (Arabic/English)
- [ ] User subscriptions are visible
- [ ] All real data is preserved
- [ ] Mobile app connectivity works

## 🚨 Rollback Plan

If something goes wrong:
```bash
# Restore database backup
mysql -u username -p database_name < production_backup.sql

# Restore file backup
tar -xzf production_backup.tar.gz -C /
```

## 📊 Verification Commands

```bash
# Check database connection
php artisan tinker --execute="echo 'DB Connection: '; DB::connection()->getPdo(); echo 'Success!';"

# Verify packages
php artisan tinker --execute="echo 'Packages: ' . App\Models\Package::count();"

# Check localization
php artisan tinker --execute="echo 'Locale: ' . app()->getLocale();"
```
