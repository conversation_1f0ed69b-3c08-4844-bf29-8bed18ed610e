#!/bin/bash

# 🚀 QatraWahda Deployment Script
# This script deploys your localhost improvements to production VPS

set -e  # Exit on any error

# Configuration - UPDATE THESE VALUES
VPS_USER="your_username"
VPS_HOST="your_vps_ip"
VPS_PATH="/path/to/your/production/project"
DB_NAME="your_database_name"
DB_USER="your_db_username"
DB_PASS="your_db_password"

echo "🚀 Starting QatraWahda Deployment..."

# Step 1: Create local backup of current state
echo "📦 Creating local backup..."
timestamp=$(date +%Y%m%d_%H%M%S)
mkdir -p ./deployment_backups
tar -czf "./deployment_backups/localhost_backup_$timestamp.tar.gz" \
    --exclude='node_modules' \
    --exclude='.git' \
    --exclude='vendor' \
    --exclude='storage/logs/*' \
    .

echo "✅ Local backup created: ./deployment_backups/localhost_backup_$timestamp.tar.gz"

# Step 2: Upload improved files to VPS
echo "📤 Uploading improved files to VPS..."

# Upload specific improved files
scp -r ./app/Http/Controllers/Dashboard/PackageController.php \
    $VPS_USER@$VPS_HOST:$VPS_PATH/app/Http/Controllers/Dashboard/

scp -r ./resources/views/dashboard/packages/ \
    $VPS_USER@$VPS_HOST:$VPS_PATH/resources/views/dashboard/

scp -r ./lang/ \
    $VPS_USER@$VPS_HOST:$VPS_PATH/

scp ./routes/web.php \
    $VPS_USER@$VPS_HOST:$VPS_PATH/routes/

echo "✅ Files uploaded successfully"

# Step 3: Execute deployment commands on VPS
echo "🔧 Executing deployment commands on VPS..."

ssh $VPS_USER@$VPS_HOST << EOF
    cd $VPS_PATH
    
    echo "📦 Creating production backup..."
    mysqldump -u $DB_USER -p$DB_PASS $DB_NAME > backup_$timestamp.sql
    
    echo "🗄️ Importing live database..."
    mysql -u $DB_USER -p$DB_PASS $DB_NAME < main.sql
    
    echo "🔄 Updating dependencies..."
    composer install --optimize-autoloader --no-dev
    
    echo "⚙️ Clearing caches..."
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan cache:clear
    
    echo "🔧 Optimizing for production..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    echo "📁 Setting permissions..."
    chown -R www-data:www-data storage bootstrap/cache
    chmod -R 775 storage bootstrap/cache
    
    echo "✅ Deployment completed on VPS"
EOF

# Step 4: Verify deployment
echo "🧪 Verifying deployment..."

ssh $VPS_USER@$VPS_HOST << EOF
    cd $VPS_PATH
    
    echo "Testing database connection..."
    php artisan tinker --execute="
        try {
            \$packages = App\Models\Package::count();
            echo 'Database OK - Packages: ' . \$packages . PHP_EOL;
        } catch (Exception \$e) {
            echo 'Database Error: ' . \$e->getMessage() . PHP_EOL;
        }
    "
    
    echo "Testing localization..."
    php artisan tinker --execute="
        echo 'Current locale: ' . app()->getLocale() . PHP_EOL;
        echo 'Arabic test: ' . __('dashboard.packages') . PHP_EOL;
    "
EOF

echo "🎉 Deployment completed successfully!"
echo ""
echo "📋 Next steps:"
echo "1. Test your website: https://your-domain.com"
echo "2. Test dashboard: https://your-domain.com/dashboard"
echo "3. Verify package management works correctly"
echo "4. Check mobile app connectivity"
echo ""
echo "🚨 If issues occur, rollback with:"
echo "   mysql -u $DB_USER -p$DB_PASS $DB_NAME < backup_$timestamp.sql"
