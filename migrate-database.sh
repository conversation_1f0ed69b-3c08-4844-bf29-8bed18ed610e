#!/bin/bash

# 🗄️ QatraWahda Database Migration Script
# Safely migrate from current database to live database with all real data

set -e

# Configuration - Using your local .env settings
DB_NAME="qatrawahda_local"
DB_USER="root"
DB_PASS=""
DB_HOST="127.0.0.1"

# XAMPP MySQL paths
MYSQL_PATH="/Applications/XAMPP/xamppfiles/bin/mysql"
MYSQLDUMP_PATH="/Applications/XAMPP/xamppfiles/bin/mysqldump"

echo "🗄️ Starting Database Migration to Live Data..."

# Create timestamp for backups
timestamp=$(date +%Y%m%d_%H%M%S)

echo "📦 Creating backup of current database..."
if [ -z "$DB_PASS" ]; then
    $MYSQLDUMP_PATH -u $DB_USER -h $DB_HOST $DB_NAME > "current_db_backup_$timestamp.sql"
else
    $MYSQLDUMP_PATH -u $DB_USER -p$DB_PASS -h $DB_HOST $DB_NAME > "current_db_backup_$timestamp.sql"
fi
echo "✅ Current database backed up to: current_db_backup_$timestamp.sql"

echo "🔍 Analyzing main.sql structure..."
echo "Checking if main.sql exists..."
if [ ! -f "main.sql" ]; then
    echo "❌ Error: main.sql file not found!"
    echo "Please ensure you have downloaded the live database as main.sql"
    exit 1
fi

echo "✅ main.sql found"

echo "📊 Comparing database structures..."
# Extract table structure from main.sql
grep "CREATE TABLE" main.sql > main_tables.txt
echo "Tables in live database (main.sql):"
cat main_tables.txt

echo ""
echo "🔄 Importing live database..."
echo "This will replace your current database with live data..."
read -p "Are you sure you want to continue? (y/N): " confirm

if [[ $confirm != [yY] ]]; then
    echo "❌ Migration cancelled"
    exit 1
fi

echo "Dropping current database..."
if [ -z "$DB_PASS" ]; then
    $MYSQL_PATH -u $DB_USER -h $DB_HOST -e "DROP DATABASE IF EXISTS $DB_NAME;"
else
    $MYSQL_PATH -u $DB_USER -p$DB_PASS -h $DB_HOST -e "DROP DATABASE IF EXISTS $DB_NAME;"
fi

echo "Creating fresh database..."
if [ -z "$DB_PASS" ]; then
    $MYSQL_PATH -u $DB_USER -h $DB_HOST -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
else
    $MYSQL_PATH -u $DB_USER -p$DB_PASS -h $DB_HOST -e "CREATE DATABASE $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
fi

echo "Importing live database..."
if [ -z "$DB_PASS" ]; then
    $MYSQL_PATH -u $DB_USER -h $DB_HOST $DB_NAME < main.sql
else
    $MYSQL_PATH -u $DB_USER -p$DB_PASS -h $DB_HOST $DB_NAME < main.sql
fi

echo "✅ Live database imported successfully"

echo "🧪 Verifying database migration..."
php artisan tinker --execute="
try {
    echo 'Testing database connection...' . PHP_EOL;
    DB::connection()->getPdo();
    echo '✅ Database connection successful' . PHP_EOL;
    
    echo 'Checking packages...' . PHP_EOL;
    \$packages = App\Models\Package::all();
    echo 'Packages found: ' . \$packages->count() . PHP_EOL;
    
    foreach(\$packages as \$package) {
        echo 'Package ID: ' . \$package->id . PHP_EOL;
        echo '  Name (raw): ' . \$package->name . PHP_EOL;
        echo '  Arabic: ' . \$package->getTranslation('name', 'ar') . PHP_EOL;
        echo '  English: ' . \$package->getTranslation('name', 'en') . PHP_EOL;
        echo '  Price: ' . \$package->price . PHP_EOL;
        echo '  Washes: ' . \$package->washes . PHP_EOL;
        echo '  Active: ' . (\$package->is_active ? 'Yes' : 'No') . PHP_EOL;
        echo '---' . PHP_EOL;
    }
    
    echo 'Checking user packages (subscriptions)...' . PHP_EOL;
    \$userPackages = App\Models\UserPackage::with(['user', 'package'])->get();
    echo 'User subscriptions found: ' . \$userPackages->count() . PHP_EOL;
    
    echo 'Checking users...' . PHP_EOL;
    \$users = App\Models\User::count();
    echo 'Users found: ' . \$users . PHP_EOL;
    
    echo 'Checking orders...' . PHP_EOL;
    \$orders = DB::table('orders')->count();
    echo 'Orders found: ' . \$orders . PHP_EOL;
    
    echo '✅ All data verification passed!' . PHP_EOL;
    
} catch (Exception \$e) {
    echo '❌ Error during verification: ' . \$e->getMessage() . PHP_EOL;
    echo 'File: ' . \$e->getFile() . ':' . \$e->getLine() . PHP_EOL;
}
"

echo ""
echo "🎉 Database Migration Completed Successfully!"
echo ""
echo "📊 Migration Summary:"
echo "  ✅ Current database backed up to: current_db_backup_$timestamp.sql"
echo "  ✅ Live database imported from: main.sql"
echo "  ✅ All data verification passed"
echo ""
echo "🔗 Your application now has:"
echo "  • Real user data from production"
echo "  • Real package data with proper localization"
echo "  • Real subscription/order data"
echo "  • All the improvements we made for package management"
echo ""
echo "🚨 If you need to rollback:"
if [ -z "$DB_PASS" ]; then
    echo "  mysql -u $DB_USER -h $DB_HOST -e \"DROP DATABASE $DB_NAME;\""
    echo "  mysql -u $DB_USER -h $DB_HOST -e \"CREATE DATABASE $DB_NAME;\""
    echo "  mysql -u $DB_USER -h $DB_HOST $DB_NAME < current_db_backup_$timestamp.sql"
else
    echo "  mysql -u $DB_USER -p$DB_PASS -h $DB_HOST -e \"DROP DATABASE $DB_NAME;\""
    echo "  mysql -u $DB_USER -p$DB_PASS -h $DB_HOST -e \"CREATE DATABASE $DB_NAME;\""
    echo "  mysql -u $DB_USER -p$DB_PASS -h $DB_HOST $DB_NAME < current_db_backup_$timestamp.sql"
fi

# Cleanup
rm -f main_tables.txt

echo ""
echo "🎯 Next steps:"
echo "1. Test the dashboard: http://localhost/QatraWahda/public"
echo "2. Verify package management works with real data"
echo "3. Test localization with real package names"
echo "4. When satisfied, deploy to production using deploy-improvements.sh"
