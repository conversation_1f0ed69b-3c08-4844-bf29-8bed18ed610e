<?php

use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Dashboard\AdminController;
use App\Http\Controllers\Dashboard\InvoiceController;
use App\Http\Controllers\Dashboard\UserController;
use App\Http\Controllers\Dashboard\WorkingHourController;
use Illuminate\Support\Facades\Route;
//use DB;
use Tymon\JWTAuth\Facades\JWTAuth;

Route::get('/', function () {
    return view('welcome');
})->name('welcome');

// Auth::routes();


Route::group(['middleware' => 'web'], function () {

    Route::get('admin-login', [LoginController::class, 'showloginform'])->name('admin-login');
    // Route::post('admin-login', [LoginController::class, 'login'])->name('admin-login');
    Route::post('/admin-login', [LoginController::class, 'login'])->name('admin-login.post');
    Route::get('admin-logout', [LoginController::class, 'logout'])->name('admin-logout');

    //dashboard.orders.get_user_addresses
    Route::get('get-user-addresses', [UserController::class, 'getUserAddresses'])->name('get_user_addresses');
});

Route::group(['middleware' => ['auth', 'admin', 'admin-lang', 'web'], 'namespace' => 'Dashboard'], function () {
    Route::get('working-hours/edit-all', [WorkingHourController::class, 'editAll'])->name('working-hours.edit-all');
    Route::put('working-hours/update-all', [WorkingHourController::class, 'updateAll'])->name('working-hours.update-all');

    Route::resource('working-hours', WorkingHourController::class);

    Route::get('admin-edit-profile', [AdminController::class, 'editProfile'])->name('admin-edit-profile');
    Route::post('update-wallet/{userId}', [UserController::class, 'updateWallet'])->name('update-wallet');
    Route::put('admin-update-profile', [AdminController::class, 'updateProfile'])->name('admin-update-profile');
    Route::get('home', [
        'uses' => 'HomeController@index',
        'as' => 'home',
        'title' => 'dashboard.home',
        'type' => 'parent',
    ]);

    /*------------ start Of roles ----------*/
    Route::get('roles', [
        'uses' => 'RoleController@index',
        'as' => 'roles.index',
        'title' => 'dashboard.roles',
        'type' => 'parent',
        'child' => ['roles.store', 'roles.update', 'roles.destroy', 'roles.deleteAll'],
    ]);

    # roles store
    Route::get('roles/create', [
        'uses' => 'RoleController@create',
        'as' => 'roles.create',
        'type' => 'child',
        'title' => ['actions.add', 'dashboard.role'],
    ]);

    # roles store
    Route::post('roles/store', [
        'uses' => 'RoleController@store',
        'as' => 'roles.store',
        'type' => 'child',
        'title' => ['actions.add', 'dashboard.role'],
    ]);

    # roles update
    Route::get('roles/{id}/edit', [
        'uses' => 'RoleController@edit',
        'as' => 'roles.edit',
        'type' => 'child',
        'title' => ['actions.edit', 'dashboard.role'],
    ]);

    # roles update
    Route::put('roles/{id}', [
        'uses' => 'RoleController@update',
        'as' => 'roles.update',
        'type' => 'child',
        'title' => ['actions.edit', 'dashboard.role'],
    ]);

    # roles delete
    Route::delete('roles/{id}', [
        'uses' => 'RoleController@destroy',
        'as' => 'roles.destroy',
        'type' => 'child',
        'title' => ['actions.delete', 'dashboard.role'],
    ]);
    #delete all roles
    Route::post('delete-all-roles', [
        'uses' => 'RoleController@deleteAll',
        'as' => 'roles.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.roles'],
    ]);
    /*------------ end Of roles ----------*/

    /*------------ start Of users ----------*/
    Route::get('users', [
        'uses' => 'UserController@index',
        'as' => 'users.index',
        'title' => 'dashboard.users',
        'type' => 'parent',
        'child' => ['users.store', 'users.update', 'users.destroy', 'users.deleteAll', 'user-attachemts'],
    ]);

    # users store
    Route::get('users/create', [
        'uses' => 'UserController@create',
        'as' => 'users.create',
        'title' => ['actions.add', 'dashboard.user'],
    ]);

    # users store
    Route::post('users/store', [
        'uses' => 'UserController@store',
        'as' => 'users.store',
        'title' => ['actions.add', 'dashboard.user'],
    ]);

    # users update
    Route::get('users/{id}/edit', [
        'uses' => 'UserController@edit',
        'as' => 'users.edit',
        'title' => ['actions.edit', 'dashboard.user'],
    ]);

    # users update
    Route::put('users/{id}', [
        'uses' => 'UserController@update',
        'as' => 'users.update',
        'title' => ['actions.edit', 'dashboard.user'],
    ]);

    # users delete
    Route::delete('users/{id}', [
        'uses' => 'UserController@destroy',
        'as' => 'users.destroy',
        'title' => ['actions.delete', 'dashboard.user'],
    ]);
    #delete all users
    Route::post('delete-all-users', [
        'uses' => 'UserController@deleteAll',
        'as' => 'users.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.users'],
    ]);
    Route::get(
        'user-attachemts/{userId}',
        [
            'uses' => 'UserController@userAttachemts',
            'as' => 'user-attachemts',
            'title' => ['actions.show', 'dashboard.user_attachments'],
        ]
    );
    /*------------ end Of users ----------*/

    /*------------ start Of admins ----------*/
    Route::get('admins', [
        'uses' => 'AdminController@index',
        'as' => 'admins.index',
        'title' => 'dashboard.admins',
        'type' => 'parent',
        'child' => ['admins.store', 'admins.update', 'admins.destroy', 'admins.deleteAll', 'activations-status', 'admin-permission'],
    ]);

    # admins store
    Route::get('admins/create', [
        'uses' => 'AdminController@create',
        'as' => 'admins.create',
        'title' => ['actions.add', 'dashboard.admin'],
    ]);

    # admins store
    Route::post('admins/store', [
        'uses' => 'AdminController@store',
        'as' => 'admins.store',
        'title' => ['actions.add', 'dashboard.admin'],
    ]);

    # admins update
    Route::get('admins/{id}/edit', [
        'uses' => 'AdminController@edit',
        'as' => 'admins.edit',
        'title' => ['actions.edit', 'dashboard.admin'],
    ]);

    # admins update
    Route::put('admins/{id}', [
        'uses' => 'AdminController@update',
        'as' => 'admins.update',
        'title' => ['actions.edit', 'dashboard.admin'],
    ]);

    # admins delete
    Route::delete('admins/{id}', [
        'uses' => 'AdminController@destroy',
        'as' => 'admins.destroy',
        'title' => ['actions.delete', 'dashboard.admin'],
    ]);
    #delete all admins
    Route::post('delete-all-admins', [
        'uses' => 'AdminController@deleteAll',
        'as' => 'admins.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.admins'],
    ]);

    Route::get('admin-permissions/{admin_id}', [
        'uses' => 'AdminController@adminPermissions',
        'as' => 'admin-permission',
        'title' => ['actions.show', 'dashboard.permissions'],
    ]);
    Route::post('activations-status/{admin_id}', [
        'uses' => 'AdminController@activationStatus',
        'as' => 'activations-status',
        'title' => ['actions.change', 'dashboard.account_status'],
    ]);

    /*------------ end Of admins ----------*/

    /*------------ start Of delegates ----------*/
    Route::get('delegates', [
        'uses' => 'DelegateController@index',
        'as' => 'delegates.index',
        'title' => 'dashboard.delegates',
        'type' => 'parent',
        'child' => ['delegates.store', 'delegates.update', 'delegates.destroy', 'delegates.deleteAll'],
    ]);

    # delegates store
    Route::get('delegates/create', [
        'uses' => 'DelegateController@create',
        'as' => 'delegates.create',
        'title' => ['actions.add', 'dashboard.delegate'],
    ]);

    # delegates store
    Route::post('delegates/store', [
        'uses' => 'DelegateController@store',
        'as' => 'delegates.store',
        'title' => ['actions.add', 'dashboard.delegate'],
    ]);

    # delegates update
    Route::get('delegates/{id}/edit', [
        'uses' => 'DelegateController@edit',
        'as' => 'delegates.edit',
        'title' => ['actions.edit', 'dashboard.delegate'],
    ]);

    # delegates update
    Route::put('delegates/{id}', [
        'uses' => 'DelegateController@update',
        'as' => 'delegates.update',
        'title' => ['actions.edit', 'dashboard.delegate'],
    ]);

    # delegates delete
    Route::delete('delegates/{id}', [
        'uses' => 'DelegateController@destroy',
        'as' => 'delegates.destroy',
        'title' => ['actions.delete', 'dashboard.delegate'],
    ]);
    #delete all delegates
    Route::post('delete-all-delegates', [
        'uses' => 'DelegateController@deleteAll',
        'as' => 'delegates.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.delegates'],
    ]);

    /*------------ end Of delegates ----------*/

    /*------------ start Of categories ----------*/
    Route::get('cars', [
        'uses' => 'CarController@index',
        'as' => 'cars.index',
        'title' => 'dashboard.cars',
        'type' => 'parent',
        'child' => ['cars.store', 'cars.update', 'cars.destroy', 'cars.deleteAll'],
    ]);

    # cars store
    Route::get('cars/create', [
        'uses' => 'CarController@create',
        'as' => 'cars.create',
        'title' => ['actions.add', 'dashboard.car'],
    ]);

    # cars store
    Route::post('cars/store', [
        'uses' => 'CarController@store',
        'as' => 'cars.store',
        'title' => ['actions.add', 'dashboard.car'],
    ]);

    # cars update
    Route::get('cars/{id}/edit', [
        'uses' => 'CarController@edit',
        'as' => 'cars.edit',
        'title' => ['actions.edit', 'dashboard.car'],
    ]);

    # cars update
    Route::put('cars/{id}', [
        'uses' => 'CarController@update',
        'as' => 'cars.update',
        'title' => ['actions.edit', 'dashboard.car'],
    ]);

    # cars delete
    Route::delete('cars/{id}', [
        'uses' => 'CarController@destroy',
        'as' => 'cars.destroy',
        'title' => ['actions.delete', 'dashboard.car'],
    ]);
    #delete all cars
    Route::post('delete-all-cars', [
        'uses' => 'CarController@deleteAll',
        'as' => 'cars.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.cars'],
    ]);
    /*------------ end Of cars ----------*/

    /*------------ start Of categories ----------*/
    Route::get('categories', [
        'uses' => 'CategoryController@index',
        'as' => 'categories.index',
        'title' => 'dashboard.categories',
        'type' => 'parent',
        'child' => ['categories.store', 'categories.update', 'categories.destroy', 'categories.deleteAll'],
    ]);

    # categories store
    Route::get('categories/create', [
        'uses' => 'CategoryController@create',
        'as' => 'categories.create',
        'title' => ['actions.add', 'dashboard.category'],
    ]);

    # categories store
    Route::post('categories/store', [
        'uses' => 'CategoryController@store',
        'as' => 'categories.store',
        'title' => ['actions.add', 'dashboard.category'],
    ]);

    # categories update
    Route::get('categories/{id}/edit', [
        'uses' => 'CategoryController@edit',
        'as' => 'categories.edit',
        'title' => ['actions.edit', 'dashboard.category'],
    ]);

    # categories update
    Route::put('categories/{id}', [
        'uses' => 'CategoryController@update',
        'as' => 'categories.update',
        'title' => ['actions.edit', 'dashboard.category'],
    ]);

    # categories delete
    Route::delete('categories/{id}', [
        'uses' => 'CategoryController@destroy',
        'as' => 'categories.destroy',
        'title' => ['actions.delete', 'dashboard.category'],
    ]);
    #delete all categories
    Route::post('delete-all-categories', [
        'uses' => 'CategoryController@deleteAll',
        'as' => 'categories.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.categories'],
    ]);
    /*------------ end Of categories ----------*/

    /*------------ start Of packages ----------*/
    Route::get('packages', [
        'uses' => 'PackageController@index',
        'as' => 'packages.index',
        'title' => 'dashboard.packages',
        'type' => 'parent',
        'child' => ['packages.store', 'packages.users', 'packages.update', 'packages.destroy', 'packages.deleteAll', 'packages.overview'],
    ]);

    Route::get('packages/overview', [
        'uses' => 'PackageController@overview',
        'as' => 'packages.overview',
        'title' => 'dashboard.packages_overview',
    ]);

    # packages store
    Route::get('packages/create', [
        'uses' => 'PackageController@create',
        'as' => 'packages.create',
        'title' => ['actions.add', 'dashboard.package'],
    ]);

    Route::get('package-users/{package_id}', [
        'uses' => 'PackageController@users',
        'as' => 'packages.users',
        'title' => ['actions.show', 'dashboard.package'],
    ]);

    // AJAX endpoints for performance optimization
    Route::get('orders/ajax-data', [
        'uses' => 'OrderController@ajaxData',
        'as' => 'orders.ajax-data',
    ]);

    # packages store
    Route::post('packages/store', [
        'uses' => 'PackageController@store',
        'as' => 'packages.store',
        'title' => ['actions.add', 'dashboard.package'],
    ]);

    # packages update
    Route::get('packages/{id}/edit', [
        'uses' => 'PackageController@edit',
        'as' => 'packages.edit',
        'title' => ['actions.edit', 'dashboard.package'],
    ]);

    # packages update
    Route::put('packages/{id}', [
        'uses' => 'PackageController@update',
        'as' => 'packages.update',
        'title' => ['actions.edit', 'dashboard.package'],
    ]);

    # packages delete
    Route::delete('packages/{id}', [
        'uses' => 'PackageController@destroy',
        'as' => 'packages.destroy',
        'title' => ['actions.delete', 'dashboard.package'],
    ]);
    #delete all packages
    Route::post('delete-all-packages', [
        'uses' => 'PackageController@deleteAll',
        'as' => 'packages.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.packages'],
    ]);
    /*------------ end Of packages ----------*/

    /*------------ start Of banners ----------*/
    Route::get('banners', [
        'uses' => 'BannerController@index',
        'as' => 'banners.index',
        'title' => 'dashboard.banners',
        'type' => 'parent',
        'child' => ['banners.store', 'banners.update', 'banners.destroy', 'banners.deleteAll'],
    ]);

    # banners store
    Route::get('banners/create', [
        'uses' => 'BannerController@create',
        'as' => 'banners.create',
        'title' => ['actions.add', 'dashboard.banner'],
    ]);

    # banners store
    Route::post('banners/store', [
        'uses' => 'BannerController@store',
        'as' => 'banners.store',
        'title' => ['actions.add', 'dashboard.banner'],
    ]);

    # banners update
    Route::get('banners/{id}/edit', [
        'uses' => 'BannerController@edit',
        'as' => 'banners.edit',
        'title' => ['actions.edit', 'dashboard.banner'],
    ]);

    # banners update
    Route::put('banners/{id}', [
        'uses' => 'BannerController@update',
        'as' => 'banners.update',
        'title' => ['actions.edit', 'dashboard.banner'],
    ]);

    # banners delete
    Route::delete('banners/{id}', [
        'uses' => 'BannerController@destroy',
        'as' => 'banners.destroy',
        'title' => ['actions.delete', 'dashboard.banner'],
    ]);
    #delete all banners
    Route::post('delete-all-banners', [
        'uses' => 'BannerController@deleteAll',
        'as' => 'banners.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.banners'],
    ]);
    /*------------ end Of banners ----------*/

    /*------------ start Of brands ----------*/
    Route::get('brands', [
        'uses' => 'BrandController@index',
        'as' => 'brands.index',
        'title' => 'dashboard.brands',
        'type' => 'parent',
        'child' => ['brands.store', 'brands.update', 'brands.destroy', 'brands.deleteAll'],
    ]);

    # brands store
    Route::get('brands/create', [
        'uses' => 'BrandController@create',
        'as' => 'brands.create',
        'title' => ['actions.add', 'dashboard.brand'],
    ]);

    # brands store
    Route::post('brands/store', [
        'uses' => 'BrandController@store',
        'as' => 'brands.store',
        'title' => ['actions.add', 'dashboard.brand'],
    ]);

    # brands update
    Route::get('brands/{id}/edit', [
        'uses' => 'BrandController@edit',
        'as' => 'brands.edit',
        'title' => ['actions.edit', 'dashboard.brand'],
    ]);

    # brands update
    Route::put('brands/{id}', [
        'uses' => 'BrandController@update',
        'as' => 'brands.update',
        'title' => ['actions.edit', 'dashboard.brand'],
    ]);

    # brands delete
    Route::delete('brands/{id}', [
        'uses' => 'BrandController@destroy',
        'as' => 'brands.destroy',
        'title' => ['actions.delete', 'dashboard.brand'],
    ]);
    #delete all brands
    Route::post('delete-all-brands', [
        'uses' => 'BrandController@deleteAll',
        'as' => 'brands.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.brands'],
    ]);
    /*------------ end Of brands ----------*/

    /*------------ start Of products ----------*/
    Route::get('products', [
        'uses' => 'ProductController@index',
        'as' => 'products.index',
        'title' => 'dashboard.products',
        'type' => 'parent',
        'child' => ['products.store', 'products.update', 'products.destroy', 'products.deleteAll'],
    ]);

    # products store
    Route::get('products/create', [
        'uses' => 'ProductController@create',
        'as' => 'products.create',
        'title' => ['actions.add', 'dashboard.product'],
    ]);

    # products store
    Route::post('products/store', [
        'uses' => 'ProductController@store',
        'as' => 'products.store',
        'title' => ['actions.add', 'dashboard.product'],
    ]);

    # products update
    Route::get('products/{id}/edit', [
        'uses' => 'ProductController@edit',
        'as' => 'products.edit',
        'title' => ['actions.edit', 'dashboard.product'],
    ]);

    # products update
    Route::put('products/{id}', [
        'uses' => 'ProductController@update',
        'as' => 'products.update',
        'title' => ['actions.edit', 'dashboard.product'],
    ]);

    # products delete
    Route::delete('products/{id}', [
        'uses' => 'ProductController@destroy',
        'as' => 'products.destroy',
        'title' => ['actions.delete', 'dashboard.product'],
    ]);
    #delete all products
    Route::post('delete-all-products', [
        'uses' => 'ProductController@deleteAll',
        'as' => 'products.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.products'],
    ]);
    /*------------ end Of products ----------*/

    /*------------ start Of works ----------*/
    Route::get('works', [
        'uses' => 'WorkController@index',
        'as' => 'works.index',
        'title' => 'dashboard.works',
        'type' => 'parent',
        'child' => ['works.store', 'works.update', 'works.destroy', 'works.deleteAll'],
    ]);

    # works store
    Route::get('works/create', [
        'uses' => 'WorkController@create',
        'as' => 'works.create',
        'title' => ['actions.add', 'dashboard.product'],
    ]);

    # works store
    Route::post('works/store', [
        'uses' => 'WorkController@store',
        'as' => 'works.store',
        'title' => ['actions.add', 'dashboard.work'],
    ]);

    # works update
    Route::get('works/{id}/edit', [
        'uses' => 'WorkController@edit',
        'as' => 'works.edit',
        'title' => ['actions.edit', 'dashboard.work'],
    ]);

    # works update
    Route::put('works/{id}', [
        'uses' => 'WorkController@update',
        'as' => 'works.update',
        'title' => ['actions.edit', 'dashboard.work'],
    ]);

    # works delete
    Route::delete('works/{id}', [
        'uses' => 'WorkController@destroy',
        'as' => 'works.destroy',
        'title' => ['actions.delete', 'dashboard.work'],
    ]);
    #delete all works
    Route::post('delete-all-works', [
        'uses' => 'WorkController@deleteAll',
        'as' => 'works.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.works'],
    ]);
    /*------------ end Of works ----------*/

    /*------------ start Of cities ----------*/
    Route::get('cities', [
        'uses' => 'CityController@index',
        'as' => 'cities.index',
        'title' => 'dashboard.cities',
        'type' => 'parent',
        'child' => ['cities.store', 'cities.update', 'cities.destroy', 'cities.deleteAll'],
    ]);

    # cities store
    Route::get('cities/create', [
        'uses' => 'CityController@create',
        'as' => 'cities.create',
        'title' => ['actions.add', 'dashboard.city'],
    ]);

    # cities store
    Route::post('cities/store', [
        'uses' => 'CityController@store',
        'as' => 'cities.store',
        'title' => ['actions.add', 'dashboard.city'],
    ]);

    # cities update
    Route::get('cities/{id}/edit', [
        'uses' => 'CityController@edit',
        'as' => 'cities.edit',
        'title' => ['actions.edit', 'dashboard.city'],
    ]);

    # cities update
    Route::put('cities/{id}', [
        'uses' => 'CityController@update',
        'as' => 'cities.update',
        'title' => ['actions.edit', 'dashboard.city'],
    ]);

    # cities delete
    Route::delete('cities/{id}', [
        'uses' => 'CityController@destroy',
        'as' => 'cities.destroy',
        'title' => ['actions.delete', 'dashboard.city'],
    ]);
    #delete all cities
    Route::post('delete-all-cities', [
        'uses' => 'CityController@deleteAll',
        'as' => 'cities.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.cities'],
    ]);
    /*------------ end Of cities ----------*/
    /*------------ start Of cities ----------*/
    Route::get('states', [
        'uses' => 'StateController@index',
        'as' => 'states.index',
        'title' => 'dashboard.states',
        'type' => 'parent',
        'child' => ['states.store', 'states.update', 'states.destroy', 'states.deleteAll'],
    ]);

    # states store
    Route::get('states/create', [
        'uses' => 'StateController@create',
        'as' => 'states.create',
        'title' => ['actions.add', 'dashboard.state'],
    ]);

    # states store
    Route::post('states/store', [
        'uses' => 'StateController@store',
        'as' => 'states.store',
        'title' => ['actions.add', 'dashboard.state'],
    ]);

    # states update
    Route::get('states/{id}/edit', [
        'uses' => 'StateController@edit',
        'as' => 'states.edit',
        'title' => ['actions.edit', 'dashboard.state'],
    ]);

    # states update
    Route::put('states/{id}', [
        'uses' => 'StateController@update',
        'as' => 'states.update',
        'title' => ['actions.edit', 'dashboard.state'],
    ]);

    # states delete
    Route::delete('states/{id}', [
        'uses' => 'StateController@destroy',
        'as' => 'states.destroy',
        'title' => ['actions.delete', 'dashboard.state'],
    ]);
    #delete all states
    Route::post('delete-all-states', [
        'uses' => 'StateController@deleteAll',
        'as' => 'states.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.states'],
    ]);
    /*------------ end Of states ----------*/

    /*------------ start Of cities ----------*/
    Route::get('targets', [
        'uses' => 'TargetController@index',
        'as' => 'targets.index',
        'title' => 'dashboard.targets',
        'type' => 'parent',
        'child' => ['targets.store', 'targets.update', 'targets.destroy', 'targets.deleteAll'],
    ]);

    # targets store
    Route::get('targets/create', [
        'uses' => 'TargetController@create',
        'as' => 'targets.create',
        'title' => ['actions.add', 'dashboard.target'],
    ]);

    # targets store
    Route::post('targets/store', [
        'uses' => 'TargetController@store',
        'as' => 'targets.store',
        'title' => ['actions.add', 'dashboard.target'],
    ]);

    # targets update
    Route::get('targets/{id}/edit', [
        'uses' => 'TargetController@edit',
        'as' => 'targets.edit',
        'title' => ['actions.edit', 'dashboard.target'],
    ]);

    # targets update
    Route::put('targets/{id}', [
        'uses' => 'TargetController@update',
        'as' => 'targets.update',
        'title' => ['actions.edit', 'dashboard.target'],
    ]);

    # targets delete
    Route::delete('targets/{id}', [
        'uses' => 'TargetController@destroy',
        'as' => 'targets.destroy',
        'title' => ['actions.delete', 'dashboard.target'],
    ]);
    #delete all targets
    Route::post('delete-all-targets', [
        'uses' => 'TargetController@deleteAll',
        'as' => 'targets.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.targets'],
    ]);
    /*------------ end Of targets ----------*/

    /*------------ start Of prices ----------*/
    Route::get('prices', [
        'uses' => 'PriceController@index',
        'as' => 'prices.index',
        'title' => 'dashboard.prices',
        'type' => 'parent',
        'child' => ['prices.store', 'prices.update', 'prices.destroy', 'prices.deleteAll'],
    ]);

    # prices store
    Route::get('prices/create', [
        'uses' => 'PriceController@create',
        'as' => 'prices.create',
        'title' => ['actions.add', 'dashboard.price'],
    ]);

    # prices store
    Route::post('prices/store', [
        'uses' => 'PriceController@store',
        'as' => 'prices.store',
        'title' => ['actions.add', 'dashboard.price'],
    ]);

    # prices update
    Route::get('prices/{id}/edit', [
        'uses' => 'PriceController@edit',
        'as' => 'prices.edit',
        'title' => ['actions.edit', 'dashboard.price'],
    ]);

    # prices update
    Route::put('prices/{id}', [
        'uses' => 'PriceController@update',
        'as' => 'prices.update',
        'title' => ['actions.edit', 'dashboard.price'],
    ]);

    # prices delete
    Route::delete('prices/{id}', [
        'uses' => 'PriceController@destroy',
        'as' => 'prices.destroy',
        'title' => ['actions.delete', 'dashboard.price'],
    ]);
    #delete all prices
    Route::post('delete-all-prices', [
        'uses' => 'PriceController@deleteAll',
        'as' => 'prices.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.prices'],
    ]);
    /*------------ end Of prices ----------*/

    /*------------ start Of coupons ----------*/
    Route::get('coupons', [
        'uses' => 'CouponController@index',
        'as' => 'coupons.index',
        'title' => 'dashboard.coupons',
        'type' => 'parent',
        'child' => ['coupons.store', 'coupons.update', 'coupons.destroy', 'coupons.deleteAll'],
    ]);

    # coupons store
    Route::get('coupons/create', [
        'uses' => 'CouponController@create',
        'as' => 'coupons.create',
        'title' => ['actions.add', 'dashboard.coupon'],
    ]);

    # coupons store
    Route::post('coupons/store', [
        'uses' => 'CouponController@store',
        'as' => 'coupons.store',
        'title' => ['actions.add', 'dashboard.coupon'],
    ]);

    # coupons update
    Route::get('coupons/{id}/edit', [
        'uses' => 'CouponController@edit',
        'as' => 'coupons.edit',
        'title' => ['actions.edit', 'dashboard.coupon'],
    ]);

    # coupons update
    Route::put('coupons/{id}', [
        'uses' => 'CouponController@update',
        'as' => 'coupons.update',
        'title' => ['actions.edit', 'dashboard.coupon'],
    ]);

    # coupons delete
    Route::delete('coupons/{id}', [
        'uses' => 'CouponController@destroy',
        'as' => 'coupons.destroy',
        'title' => ['actions.delete', 'dashboard.coupon'],
    ]);
    #delete all coupons
    Route::post('delete-all-coupons', [
        'uses' => 'CouponController@deleteAll',
        'as' => 'coupons.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.coupons'],
    ]);
    /*------------ end Of coupons ----------*/



    /*------------ start Of offers ----------*/
    Route::get('offers', [
        'uses' => 'OfferController@index',
        'as' => 'offers.index',
        'title' => 'dashboard.offers',
        'type' => 'parent',
        'child' => ['offers.store', 'offers.update', 'offers.destroy', 'offers.deleteAll'],
    ]);

    # offers store
    Route::get('offers/create', [
        'uses' => 'OfferController@create',
        'as' => 'offers.create',
        'title' => ['actions.add', 'dashboard.offer'],
    ]);

    # offers store
    Route::post('offers/store', [
        'uses' => 'OfferController@store',
        'as' => 'offers.store',
        'title' => ['actions.add', 'dashboard.offer'],
    ]);

    # offers update
    Route::get('offers/{id}/edit', [
        'uses' => 'OfferController@edit',
        'as' => 'offers.edit',
        'title' => ['actions.edit', 'dashboard.offer'],
    ]);

    # offers update
    Route::put('offers/{id}', [
        'uses' => 'OfferController@update',
        'as' => 'offers.update',
        'title' => ['actions.edit', 'dashboard.offer'],
    ]);

    # offers delete
    Route::delete('offers/{id}', [
        'uses' => 'OfferController@destroy',
        'as' => 'offers.destroy',
        'title' => ['actions.delete', 'dashboard.offer'],
    ]);
    #delete all offers
    Route::post('delete-all-offers', [
        'uses' => 'OfferController@deleteAll',
        'as' => 'offers.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.offers'],
    ]);
    /*------------ end Of offers ----------*/

    /*------------ start Of invoices ----------*/
    Route::get('invoices', [
        'uses' => 'InvoiceController@index',
        'as' => 'invoices.index',
        'title' => 'dashboard.invoices',
        'type' => 'parent',
        'child' => ['invoices.show', 'invoices.store', 'invoices.update', 'invoices.destroy', 'invoices.deleteAll'],
    ]);
    Route::get('invoices/{id}/Show', [
        'uses'  => 'InvoiceController@show',
        'as'    => 'invoices.show',
        'title' => ['actions.show', 'dashboard.invoices']
    ]);
    # invoices store
    Route::get('invoices/create', [
        'uses' => 'InvoiceController@create',
        'as' => 'invoices.create',
        'title' => ['actions.add', 'dashboard.invoice'],
    ]);

    # invoices store
    Route::post('invoices/store', [
        'uses' => 'InvoiceController@store',
        'as' => 'invoices.store',
        'title' => ['actions.add', 'dashboard.invoice'],
    ]);

    # invoices update
    Route::get('invoices/{id}/edit', [
        'uses' => 'InvoiceController@edit',
        'as' => 'invoices.edit',
        'title' => ['actions.edit', 'dashboard.invoice'],
    ]);
    # invoices paidCash
    Route::put('invoices/{id}/paid', [
        'uses' => 'InvoiceController@paidCash',
        'as' => 'invoices.paidCash',
        'title' => ['actions.paidCash', 'dashboard.invoice'],
    ]);

    # invoices update
    Route::put('invoices/{id}', [
        'uses' => 'InvoiceController@update',
        'as' => 'invoices.update',
        'title' => ['actions.edit', 'dashboard.invoice'],
    ]);

    # invoices delete
    Route::delete('invoices/{id}', [
        'uses' => 'InvoiceController@destroy',
        'as' => 'invoices.destroy',
        'title' => ['actions.delete', 'dashboard.invoice'],
    ]);
    #delete all invoices
    Route::post('delete-all-invoices', [
        'uses' => 'InvoiceController@deleteAll',
        'as' => 'invoices.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.invoices'],
    ]);
    /*------------ end Of invoices ----------*/

    //withdraws routes
    Route::get('withdraws', [
        'uses'      => 'WithdrawController@index',
        'as'        => 'withdraws.index',
        'title'     => 'dashboard.withdraws',
        'type'      => 'parent',
        'sub_route' => false,
        'child'     => ['withdraws.show', 'withdraws.destroy', 'withdraws.updateStatus', 'withdraws.deleteAll',]
    ]);

    # withdraws show
    Route::get('withdraws/{id}/Show', [
        'uses'  => 'WithdrawController@show',
        'as'    => 'withdraws.show',
        'title' => ['actions.show', 'dashboard.withdraw']
    ]);

    Route::post('withdraws/{id}/status', [
        'uses'  => 'WithdrawController@updateStatus',
        'as'    => 'withdraws.updateStatus',
        'title' => ['actions.edit', 'dashboard.withdraw']
    ]);

    # withdraws delete
    Route::delete('withdraws/{id}', [
        'uses'  => 'WithdrawController@destroy',
        'as'    => 'withdraws.destroy',
        'title' => ['actions.delete', 'dashboard.withdraw']
    ]);

    #delete all withdraws
    Route::post('delete-all-withdraws', [
        'uses'  => 'WithdrawController@deleteAll',
        'as'    => 'withdraws.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.withdraws']
    ]);

    /*------------ start Of transactions ----------*/
    Route::get('transactions', [
        'uses'      => 'TransactionController@index',
        'as'        => 'transactions.index',
        'title'     => 'dashboard.transactions',
        'type'      => 'parent',
        'sub_route' => false,
        'child'     => ['transactions.create', 'transactions.edit', 'transactions.show', 'transactions.destroy', 'transactions.deleteAll',]
    ]);

    # transactions store
    Route::get('transactions/create', [
        'uses'  => 'TransactionController@create',
        'as'    => 'transactions.create',
        'title' => ['actions.add', 'dashboard.transaction']
    ]);


    # transactions store
    Route::post('transactions/store', [
        'uses'  => 'TransactionController@store',
        'as'    => 'transactions.store',
        'title' => ['actions.add', 'dashboard.transaction']

    ]);

    # transactions update
    Route::get('transactions/{id}/edit', [
        'uses'  => 'TransactionController@edit',
        'as'    => 'transactions.edit',
        'title' => ['actions.edit', 'dashboard.transaction']
    ]);

    # transactions update
    Route::put('transactions/{id}', [
        'uses'  => 'TransactionController@update',
        'as'    => 'transactions.update',
        'title' => ['actions.edit', 'dashboard.transaction']
    ]);

    # transactions show
    Route::get('transactions/{id}/Show', [
        'uses'  => 'TransactionController@show',
        'as'    => 'transactions.show',
        'title' => ['actions.show', 'dashboard.transaction']
    ]);

    # transactions delete
    Route::delete('transactions/{id}', [
        'uses'  => 'TransactionController@destroy',
        'as'    => 'transactions.destroy',
        'title' => ['actions.delete', 'dashboard.transaction']
    ]);
    #delete all transactions
    Route::post('delete-all-transactions', [
        'uses'  => 'TransactionController@deleteAll',
        'as'    => 'transactions.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.transactions']
    ]);
    /*------------ end Of transactions ----------*/

    /*------------ start Of orders ----------*/
    Route::get('orders', [
        'uses' => 'OrderController@index',
        'as' => 'orders.index',
        'title' => 'dashboard.orders',
        'type' => 'parent',
        'child' => ['orders.store', 'orders.update', 'orders.show', 'user-orders', 'orders.destroy', 'orders.deleteAll'],
    ]);

    # orders store
    Route::get('orders/create', [
        'uses' => 'OrderController@create',
        'as' => 'orders.create',
        'title' => ['actions.add', 'dashboard.user_order'],
    ]);

    # orders store
    Route::post('orders/store', [
        'uses' => 'OrderController@store',
        'as' => 'orders.store',
        'title' => ['actions.add', 'dashboard.user_order'],
    ]);

    # orders update
    Route::get('orders/{id}/edit', [
        'uses' => 'OrderController@edit',
        'as' => 'orders.edit',
        'title' => ['actions.edit', 'dashboard.user_order'],
    ]);

    # orders update
    Route::put('orders/{id}', [
        'uses' => 'OrderController@update',
        'as' => 'orders.update',
        'title' => ['actions.edit', 'dashboard.user_order'],
    ]);
    // Route for updating the payment link for a specific order
    Route::put('orders/{id}/paymentLink', [
        'uses' => 'OrderController@paymentLink', // Specifies the controller and method handling the route
        'as' => 'orders.paymentLink', // Route name for easier usage
        'title' => [
            'actions.edit',          // Action type (e.g., editing an entity)
            'dashboard.user_order',  // Context for this action (e.g., user orders in dashboard)
        ],
    ]);

    # orders show
    Route::get('orders/{id}', [
        'uses' => 'OrderController@show',
        'as' => 'orders.show',
        'title' => ['actions.show', 'dashboard.user_order'],
    ]);

    # orders delete
    Route::delete('orders/{id}', [
        'uses' => 'OrderController@destroy',
        'as' => 'orders.destroy',
        'title' => ['actions.delete', 'dashboard.user_order'],
    ]);
    #delete all orders
    Route::post('delete-all-orders', [
        'uses' => 'OrderController@deleteAll',
        'as' => 'orders.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.orders'],
    ]);

    Route::post('assign-order', [
        'uses' => 'OrderController@assignOrder',
        'as' => 'orders.assign-order',
        'title' => ['actions.edit', 'dashboard.user_order'],
    ]);

    Route::get('user-orders/{user_id}', [
        'uses' => 'OrderController@userOrders',
        'as' => 'user-orders',
        'title' => ['actions.show', 'dashboard.orders'],
    ]);
    Route::get('delegate-orders/{user_id}', [
        'uses' => 'OrderController@DelegateOrders',
        'as' => 'delegate-orders',
        'title' => ['actions.show', 'dashboard.orders'],
    ]);
    Route::get('orders-by-status/{status}', [
        'uses' => 'OrderController@ordersByStatus',
        'as' => 'orders.orders-by-status',
        'title' => ['actions.show', 'dashboard.user_orders_by_status'],
    ]);
    /*------------ end Of orders ----------*/

    /*------------ start Of Settings----------*/
    Route::get('settings', [
        'uses' => 'SettingController@index',
        'as' => 'settings',
        'title' => 'dashboard.settings',
        'type' => 'parent',
        'child' => [
            'update-settings',
            'sms-update',
        ],
    ]);

    #update
    Route::post('settings', [
        'uses' => 'SettingController@update',
        'as' => 'update-settings',
        'title' => ['actions.edit', 'dashboard.settings'],
    ]);

    #message all
    Route::post('settings/{type}/message-all', [
        'uses' => 'SettingController@messageAll',
        'as' => 'settings.message.all',
        'title' => ['actions.send', 'dashboard.all_users'],
    ])->where('type', 'email|sms|notification');

    #message one
    Route::post('settings/{type}/message-one', [
        'uses' => 'SettingController@messageOne',
        'as' => 'settings.message.one',
        'title' => ['actions.send', 'dashboard.user'],
    ])->where('type', 'email|sms|notification');

    #send email
    Route::post('settings/send-email', [
        'uses' => 'SettingController@sendEmail',
        'as' => 'settings.send_email',
        'title' => ['actions.send_email', 'dashboard.user'],

    ]);

    Route::post('sms-update', [
        'uses' => 'SettingController@updateSms',
        'as' => 'sms-update',
        'title' => ['actions.edit', 'dashboard.sms_providers'],
    ]);

    Route::get('set-lang/{lang}', [
        'uses' => 'SettingController@SetLanguage',
        'as' => 'set-lang',
        'title' => 'dashboard.set_lang',
    ]);

    /*------------ end Of Settings ----------*/

    /*------------ start Of notifications ----------*/
    Route::get('notifications', [
        'uses' => 'NotificationController@index',
        'as' => 'notifications.index',
        'title' => 'dashboard.notifications',
        'type' => 'parent',
        'child' => ['notifications.store', 'notifications.destroy', 'notifications.deleteAll'],
    ]);

    # notifications store
    Route::get('notifications/create', [
        'uses' => 'NotificationController@create',
        'as' => 'notifications.create',
        'title' => ['actions.add', 'dashboard.notification'],
    ]);

    # notifications store
    Route::post('notifications/store', [
        'uses' => 'NotificationController@store',
        'as' => 'notifications.store',
        'title' => ['actions.add', 'dashboard.notification'],
    ]);

    # notifications delete
    Route::delete('notifications/{id}', [
        'uses' => 'NotificationController@destroy',
        'as' => 'notifications.destroy',
        'title' => ['actions.delete', 'dashboard.notification'],
    ]);
    #delete all notifications
    Route::post('delete-all-notifications', [
        'uses' => 'NotificationController@deleteAll',
        'as' => 'notifications.deleteAll',
        'title' => ['actions.delete_all', 'dashboard.notifications'],
    ]);

    Route::post('send-notification', [

        'uses' => 'NotificationController@sendNotification',
        'as' => 'send-notification',
        'title' => 'dashboard.send_notification',
    ]);

    Route::post('storeToken', [
        'uses' => 'NotificationController@storeToken',
        'as' => 'notifications.storeToken',
        'title' => 'dashboard.store_token',
    ]);
    Route::post('notify', [
        'uses' => 'NotificationController@notify',
        'as' => 'notify',
        'title' => 'dashboard.send_one_notification',
    ]);

    /*------------ end Of notifications ----------*/
});
Route::get('bundled-services-offer/{id}', [InvoiceController::class, 'show'])
    ->name('bundled-services-offer')
    ->defaults('title', ['actions.show', 'dashboard.invoices']);
