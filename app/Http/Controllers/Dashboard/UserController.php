<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Repositories\IPermissionRepository;
use App\Repositories\IRoleRepository;
use App\Repositories\IUserRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
    private $usersRepository;
    private $rolesRepository;
    private $permissionRepository;

    public function __construct(IUserRepository $usersRepository,
        IRoleRepository $rolesRepository,
        IPermissionRepository $permissionRepository) {

        $this->usersRepository = $usersRepository;
        $this->rolesRepository = $rolesRepository;
        $this->permissionRepository = $permissionRepository;
    }

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        // Build query with filters
        $query = \App\Models\User::whereIn('user_type', [2, 3])
            ->select(['id', 'name', 'email', 'phone', 'user_type', 'is_active', 'is_blocked', 'created_at']);

        // Apply search filter
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        // Apply user type filter
        if ($request->filled('user_type') && $request->user_type !== 'all') {
            $query->where('user_type', $request->user_type);
        }

        // Apply status filter
        if ($request->filled('status') && $request->status !== 'all') {
            if ($request->status === 'active') {
                $query->where('is_active', 1);
            } elseif ($request->status === 'inactive') {
                $query->where('is_active', 0);
            } elseif ($request->status === 'blocked') {
                $query->where('is_blocked', 1);
            }
        }

        // Apply date range filter
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Get paginated results
        $users = $query->orderBy('created_at', 'DESC')->paginate(25);

        // Preserve query parameters in pagination links
        $users->appends($request->query());

        // Add statistics for dashboard (with same filters for accuracy)
        $statsQuery = \App\Models\User::whereIn('user_type', [2, 3]);

        // Apply same filters to stats
        if ($request->filled('search')) {
            $search = $request->search;
            $statsQuery->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        if ($request->filled('user_type') && $request->user_type !== 'all') {
            $statsQuery->where('user_type', $request->user_type);
        }

        if ($request->filled('date_from')) {
            $statsQuery->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $statsQuery->whereDate('created_at', '<=', $request->date_to);
        }

        $stats = [
            'total_users' => $statsQuery->count(),
            'active_users' => (clone $statsQuery)->where('is_active', 1)->count(),
            'inactive_users' => (clone $statsQuery)->where('is_active', 0)->count(),
            'blocked_users' => (clone $statsQuery)->where('is_blocked', 1)->count(),
        ];

        return view('dashboard.users.index', compact('users', 'stats'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        return view('dashboard.users.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $data = $request->all();
        $data['user_type'] = 2;
        return $this->usersRepository->create($data);
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        $user = $this->usersRepository->findOne($id);

        return view('dashboard.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        $user = $this->usersRepository->findOne($id);
        return view('dashboard.users.edit', compact('user'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {

        return $this->usersRepository->update($request->validated(), $id);
    }

    public function updateWallet(Request $request, $id)
    {

        return $this->usersRepository->update(['wallet_balance' => $request->wallet_balance], $id);
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        return $this->usersRepository->forceDelete($id);
    }

    public function userAttachemts($userId)
    {
        $user = $this->usersRepository->findOne($userId);
        return view('dashboard.users.attachments', compact('user'));

    }

    public function deleteAll(Request $request)
    {
        $requestIds = json_decode($request->data);

        foreach ($requestIds as $id) {
            $ids[] = $id->id;
        }
        if ($this->usersRepository->deleteForceWhereIn('id', $ids)) {
            return response()->json('success');
        } else {
            return response()->json('failed');
        }
    }

    //getUserAddresses
    public function getUserAddresses(Request $request)
    {
        $user = $this->usersRepository->findOne($request->user_id);
        return response()->json($user->addresses);
    }

}
