<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\Package;
use App\Models\UserPackage;
use App\Repositories\IPackageRepository;
use App\Requests\dashboard\CreateUpdatePackageRequest;
use Illuminate\Http\Request;
use App\Repositories\ICarRepository;
use App\Repositories\ICategoryRepository;

use Illuminate\Support\Facades\Storage;

class PackageController extends Controller
{
    private $packageRepository;
 
    public function __construct(IPackageRepository $packageRepository , 
                               ICategoryRepository $catRepository ,
                               ICarRepository $carRepository){

        $this->packageRepository = $packageRepository;
        $this->catRepository = $catRepository;
        $this->carRepository = $carRepository;


    }


    public function index()
    {
        $packages = Package::with('userpackages')->orderBy('id', 'DESC')->get();
        return view('dashboard.packages.index' , compact('packages'));
    }

    public function overview()
    {
        $packages = Package::with('userpackages.user')->orderBy('id', 'DESC')->get();

        // Calculate statistics
        $totalPackages = $packages->count();
        $activePackages = $packages->where('is_active', 1)->count();

        $allSubscriptions = UserPackage::with('user')->get();
        $totalSubscriptions = $allSubscriptions->count();
        $activeSubscriptions = $allSubscriptions->filter(function($sub) {
            return $sub->expire_at && \Carbon\Carbon::parse($sub->expire_at)->isFuture();
        })->count();

        $totalUsers = $allSubscriptions->count();
        $uniqueUsers = $allSubscriptions->pluck('user_id')->unique()->count();

        $totalWashes = $allSubscriptions->sum('washes');
        $remainingWashes = $allSubscriptions->sum('remain_washes');

        return view('dashboard.packages.overview', compact(
            'packages', 'totalPackages', 'activePackages', 'totalSubscriptions',
            'activeSubscriptions', 'totalUsers', 'uniqueUsers', 'totalWashes', 'remainingWashes'
        ));
    }

    public function users($package_id)
    {
        $package = $this->packageRepository->findOne($package_id);
        $subscriptions = UserPackage::with(['user', 'package'])
            ->where('package_id', $package_id)
            ->latest()
            ->get();

        return view('dashboard.packages.users', compact('subscriptions', 'package'));
    }

    public function create()
    {
        $categories = $this->catRepository->getAll(['column' => 'id', 'dir' => 'DESC']);
        $cars = $this->carRepository->getAll(['column' => 'id', 'dir' => 'DESC']);
        return view('dashboard.packages.create' , compact('cars', 'categories'));
    }

    public function store(CreateUpdatePackageRequest $request)
    {
        $this->packageRepository->create($request->all());
        return response()->json();
    }


    public function edit($id)
    {
        $package = $this->packageRepository->findOne($id);
        $categories = $this->catRepository->getAll(['column' => 'id', 'dir' => 'DESC']);
        $cars = $this->carRepository->getAll(['column' => 'id', 'dir' => 'DESC']);
        return view('dashboard.packages.edit' , compact('package','cars', 'categories'));
    }

    public function update(CreateUpdatePackageRequest $request , $id)
    {
        $this->packageRepository->update($request->validated() , $id);
        return response()->json();
    }


    public function destroy($id)
    {
        $this->packageRepository->forceDelete($id);
        return response()->json();

    }

    public function deleteAll(Request $request) {
        $requestIds = json_decode($request->data);
    
        foreach ($requestIds as $id) {
          $ids[] = $id->id;
        }
        if ($this->packageRepository->deleteForceWhereIn('id', $ids)) {
          return response()->json('success');
        } else {
          return response()->json('failed');
        }
    }


}