<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Models\CategoryCar;
use App\Models\PaymentType;
use App\Models\User;
use App\Notifications\OrderNotification;
use App\Repositories\ICarRepository;
use App\Repositories\ICategoryRepository;
use App\Repositories\ICouponRepository;
use App\Repositories\IOrderRepository;
use App\Repositories\IUserRepository;
use App\Requests\dashboard\CreateUpdateOrderRequest;
use App\Services\MyFatoorahService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    private $orderRepository;
    private $Iuser;
    private $Icat;
    private $Icar;
    private $Icoupon;
    private $myFatoorahService;

    public function __construct(
        IOrderRepository $orderRepository,
        IUserRepository $Iuser,
        ICarRepository $Icar,
        ICategoryRepository $Icat,
        ICouponRepository $Icoupon,
        MyFatoorahService $myFatoorahService,
    ) {

        $this->orderRepository = $orderRepository;
        $this->Iuser = $Iuser;
        $this->Icat = $Icat;
        $this->Icar = $Icar;
        $this->Icoupon = $Icoupon;
        $this->myFatoorahService = $myFatoorahService;
    }

    public function index(Request $request)
    {
        try {
            // Build query with filters
            $query = \App\Models\Order::with(['user:id,name,phone', 'delegate:id,name'])
                ->select(['id', 'order_num', 'user_id', 'delegate_id', 'status', 'payment_type', 'total', 'created_at']);

            // Apply search filter
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('order_num', 'like', "%{$search}%")
                      ->orWhereHas('user', function($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                      });
                });
            }

            // Apply status filter
            if ($request->filled('status') && $request->status !== 'all') {
                $query->where('status', $request->status);
            }

            // Apply payment type filter
            if ($request->filled('payment_type') && $request->payment_type !== 'all') {
                $query->where('payment_type', $request->payment_type);
            }

            // Apply date range filter
            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }
            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            // Apply delegate filter
            if ($request->filled('delegate_id') && $request->delegate_id !== 'all') {
                $query->where('delegate_id', $request->delegate_id);
            }

            // Get paginated results
            $orders = $query->orderBy('created_at', 'DESC')->paginate(25);

            // Preserve query parameters in pagination links
            $orders->appends($request->query());

            $delegates = \App\Models\User::where('user_type', 3)->get(['id', 'name']);

            // Add statistics for dashboard (with same filters for accuracy)
            $statsQuery = \App\Models\Order::query();

            // Apply same filters to stats
            if ($request->filled('search')) {
                $search = $request->search;
                $statsQuery->where(function($q) use ($search) {
                    $q->where('order_num', 'like', "%{$search}%")
                      ->orWhereHas('user', function($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('phone', 'like', "%{$search}%");
                      });
                });
            }

            if ($request->filled('date_from')) {
                $statsQuery->whereDate('created_at', '>=', $request->date_from);
            }
            if ($request->filled('date_to')) {
                $statsQuery->whereDate('created_at', '<=', $request->date_to);
            }

            if ($request->filled('delegate_id') && $request->delegate_id !== 'all') {
                $statsQuery->where('delegate_id', $request->delegate_id);
            }

            $stats = [
                'total_orders' => $statsQuery->count(),
                'pending_orders' => (clone $statsQuery)->where('status', 'pending')->count(),
                'completed_orders' => (clone $statsQuery)->where('status', 'done')->count(),
                'cancelled_orders' => (clone $statsQuery)->where('status', 'cancelled')->count(),
            ];

            return view('dashboard.orders.index', compact('orders', 'delegates', 'stats'));

        } catch (\Exception $e) {
            // Fallback to simple pagination if there's an error
            $orders = \App\Models\Order::with(['user:id,name,phone', 'delegate:id,name'])
                ->orderBy('created_at', 'DESC')
                ->paginate(25);

            $delegates = \App\Models\User::where('user_type', 3)->get(['id', 'name']);

            $stats = [
                'total_orders' => \App\Models\Order::count(),
                'pending_orders' => \App\Models\Order::where('status', 'pending')->count(),
                'completed_orders' => \App\Models\Order::where('status', 'done')->count(),
                'cancelled_orders' => \App\Models\Order::where('status', 'cancelled')->count(),
            ];

            return view('dashboard.orders.index', compact('orders', 'delegates', 'stats'));
        }
    }

    public function create()
    {
        // dd('asdfgsdg');
        $paymentType=PaymentType::all();
        $users = $this->Iuser->AllUsers();
        $cars = $this->Icar->getAllActive();
        $categories = $this->Icat->getAllActive();
        $delegates = $this->Iuser->AllDelegates();
        return view('dashboard.orders.create', compact('users', 'cars', 'categories', 'delegates','paymentType'));
    }

    public function store(CreateUpdateOrderRequest $request)
    {
        // if ($request->filled('user_id')) {
        //     $request->request->remove('new_user_phone');
        //     $request->request->remove('country_code');
        // }
        $car         = $this->Icar->findOne($request->car_id);
        $category    = $this->Icat->findOne($request->category_id);

        $total_price = $this->calculatePriceWithoutCoupon($car, $category);

        $coupon =  $this->Icoupon->findOne($request->coupon_id);
        if ($coupon) {

            $total_price =  $this->calculatePrice($car, $category, $coupon);
        }

        if ($car) {
            // Check if user_id is provided; if not, create a new user using the phone number and country code
            if (!$request->has('user_id') || empty($request->user_id)) {
                // Validate phone and country code to ensure proper format

                // Create a new user with the provided phone number and default values for name and password
                $newUser = User::create([
                    'phone'    => $request->new_user_phone,
                    'country_code'    => $request->country_code,
                ]);

                // Add the newly created user's ID to the request data
                $request->merge(['user_id' => $newUser->id]);
            }
            $this->orderRepository->create($request->all() + [
                'cost_before_discount' =>  $this->calculatePriceWithoutCoupon($car, $category),
                'final_cost' => $total_price
            ]);
        }
        return response()->json();
    }


    public function calculatePrice($car, $category, $coupon)
    {
        $price = $this->calculatePriceWithoutCoupon($car, $category);

        if (!$coupon || $coupon->expires_on->isPast() || $coupon->times == 0) {

            return $price;
        }
        if ($coupon->percentage) {
            $total_price =  $price - (($price * $coupon->discount) / 100);
            $coupon->update(['times' => $coupon->times - 1]);
        } else {
            $total_price =  $price - $coupon->discount;
            $coupon->update(['times' => $coupon->times - 1]);
        }

        return $total_price;
    }


    public function calculatePriceWithoutCoupon($car, $category)
    {
        $total_price = CategoryCar::where(['category_id' => $category->id, 'car_id' => $car->id])->first();
        $price =  isset($total_price) ? $total_price->price : 0;

        return $price;
    }

    public function edit($id)
    {
        $order = $this->orderRepository->findOne($id);
        $users = $this->Iuser->AllUsers();
        $categories = $this->Icat->getAllActive();
        $cars = $this->Icar->getAllActive();
        return view('dashboard.orders.edit', compact('order', 'users', 'categories', 'cars'));
    }

    public function update(CreateUpdateOrderRequest $request, $id)
    {
        $this->orderRepository->update($request->validated(), $id);
        return response()->json();
    }

    public function paymentLink(Request $request, $order_id)
    {
        // 🔍 Find the order by ID
        $order = $this->orderRepository->findOne($order_id);

        // ❌ If the order does not exist, return a 404 response
        if (!$order) {
            return response()->json(['message' => 'Order not found'], 404);
        }

        // ❌ Check if the order has an associated user
        if (!$order->user) {
            return response()->json(['message' => 'User not found for this order'], 400);
        }

        // ❌ Validate that the order has a valid final cost
        if (!$order->final_cost || $order->final_cost <= 0) {
            return response()->json(['message' => 'Invalid order total cost'], 400);
        }

        // ✅ Prepare invoice data for MyFatoorah payment service
        $invoiceUserData = [
            'id'        => $order->id,
            'key'    => $order->order_num,
            'name'   => $order->user->name ?? 'Unknown User', // Prevent error if name is null
            'email'  => $order->user->email ?? '<EMAIL>', // Default email if missing
            'total'         => $order->final_cost,
            'callbackURL'    => route('payment.callback',$order_id),
            'country_code'   => $order->user->country_code ?? '+965', // Default country code
            'phone' => $order->user->phone ?? '12345678', // Default phone number if missing
        ];

        try {
            // 🔄 Request the payment link from MyFatoorah API
            $payment = $this->myFatoorahService->getInvoiceURL($invoiceUserData, $request->input('pmid', 0), $request->input('sid', null));

            // ❌ If MyFatoorah API did not return an invoice URL, throw an error
            if (!isset($payment['invoiceURL'])) {
                throw new \Exception('Failed to generate payment link.');
            }

            // ✅ Update the order with the generated payment URL and payment type
            $order->update([
                'payment_url'  => $payment['invoiceURL'],
                'payment_type' => 'myFatoorah'
            ]);

            // ✅ Return the payment URL in a successful JSON response
            return response()->json(['payment_url' => $payment['invoiceURL']], 200);

        } catch (\Exception $e) {
            // 🔴 Log the error with user details for debugging
            Log::error('Payment Link Generation Error: ' . $e->getMessage(), [
                'name'     => $order->user->name ?? 'Unknown', // Prevent error if name is missing
                'user_id'  => $order->user->id ?? 'N/A' // Avoid null error
            ]);

            // ❌ Return a JSON error response
            return response()->json(['message' => 'Payment link generation failed. Please try again.'], 500);
        }
    }




    public function assignOrder(Request $request)
    {

        $order = $this->orderRepository->findOne($request->order_id);

        $order->update(['status' => 'preparing', 'delegate_id' => $request->delegate_id]);

        $order->delegate->update(['wallet_balance' => $order->delegate->wallet_balance + $order->final_cost]);

        $dtokens = $order->delegate->devices->pluck('device_id')->toArray();
        $dplatforms = $order->delegate->devices->pluck('device_type')->toArray();
        $utokens = $order->user->devices->pluck('device_id')->toArray();
        $uplatforms = $order->user->devices->pluck('device_type')->toArray();
        $order->delegate->notify(new OrderNotification($order, $dtokens, 'new-order', $dplatforms));
        $order->user->notify(new OrderNotification($order, $utokens, 'order-status', $uplatforms));

        return response()->json();
    }

    public function destroy($id)
    {
        $this->orderRepository->forceDelete($id);
        return response()->json();
    }

    public function show($id)
    {
        $order = $this->orderRepository->findOne($id);
        return view('dashboard.orders.show', compact('order'));
    }

    public function ordersByStatus($status)
    {
        $statues = [];
        if ($status == "pending") {
            $statues = ['pending'];
        }

        if ($status == "preparing") {
            $statues = ['preparing', 'delegate_accept'];
        }

        if ($status == "done") {
            $statues = ['done'];
        }

        if ($status == "cancelled") {
            $statues = ['cancelled'];
        }

        $orders = $this->orderRepository->getWhereIn('status', $statues);
        $delegates = $this->Iuser->AllDelegates();

        return view('dashboard.orders.index', compact('orders', 'delegates'));
    }

    public function userOrders($userId)
    {
        $orders = $this->orderRepository->getWhere(['user_id' => $userId]);
        $delegates = $this->Iuser->AllDelegates();

        return view('dashboard.users.orders', compact('orders', 'delegates'));
    }

    public function DelegateOrders($userId)
    {
        $orders = $this->orderRepository->getWhere(['delegate_id' => $userId]);
        $delegates = $this->Iuser->AllDelegates();

        return view('dashboard.delegates.orders', compact('orders', 'delegates'));
    }

    /**
     * AJAX endpoint for DataTables server-side processing
     */
    public function ajaxData(Request $request)
    {
        $draw = $request->get('draw');
        $start = $request->get('start');
        $length = $request->get('length');
        $search = $request->get('search')['value'];
        $orderColumn = $request->get('order')[0]['column'] ?? 0;
        $orderDir = $request->get('order')[0]['dir'] ?? 'desc';

        // Column mapping for ordering
        $columns = ['id', 'order_num', 'user.name', 'status', 'total', 'created_at'];
        $orderBy = $columns[$orderColumn] ?? 'created_at';

        // Build query
        $query = \App\Models\Order::with(['user:id,name,phone', 'delegate:id,name'])
            ->select(['id', 'order_num', 'user_id', 'delegate_id', 'status', 'total', 'created_at']);

        // Apply search
        if (!empty($search)) {
            $query->where(function($q) use ($search) {
                $q->where('order_num', 'like', "%{$search}%")
                  ->orWhereHas('user', function($userQuery) use ($search) {
                      $userQuery->where('name', 'like', "%{$search}%")
                               ->orWhere('phone', 'like', "%{$search}%");
                  });
            });
        }

        // Apply filters
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        if ($request->has('date_range') && !empty($request->date_range)) {
            $dates = explode(' to ', $request->date_range);
            if (count($dates) == 2) {
                $query->whereBetween('created_at', [$dates[0], $dates[1]]);
            }
        }

        // Get total count before pagination
        $totalRecords = $query->count();

        // Apply ordering and pagination
        $orders = $query->orderBy($orderBy, $orderDir)
                       ->skip($start)
                       ->take($length)
                       ->get();

        // Format data for DataTables
        $data = $orders->map(function($order) {
            return [
                'id' => $order->id,
                'order_num' => $order->order_num,
                'user_name' => $order->user ? $order->user->name : 'N/A',
                'user_phone' => $order->user ? $order->user->phone : 'N/A',
                'delegate_name' => $order->delegate ? $order->delegate->name : 'غير محدد',
                'status' => $order->status,
                'total' => $order->total,
                'created_at' => $order->created_at->format('Y-m-d H:i'),
                'actions' => view('dashboard.orders.partials.actions', compact('order'))->render()
            ];
        });

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }
}
