@extends('dashboard.layouts.app')
@section('pageTitle', __('dashboard.orders'))

@section('content')

<!--begin::Modal - Customers - Add-->
<div class="modal fade" id="kt_modal_add_customer" tabindex="-1" aria-hidden="true">
    <!--begin::Modal dialog-->
    <div class="modal-dialog modal-dialog-centered mw-650px">
        <!--begin::Modal content-->
        <div class="modal-content">
            <!--begin::Form-->
            <form class="form assign-form"  action="{{route('orders.assign-order')}}" method="POST" id="kt_modal_add_customer_form" data-kt-redirect="{{route('orders.index')}}">
              @csrf
                <input type="hidden" name="order_id" class="order_id">
                <!--begin::Modal header-->
                <div class="modal-header" id="kt_modal_add_customer_header">
                    <!--begin::Modal title-->
                    <h2 class="fw-bold">@lang('dashboard.assign_order')</h2>
                    <!--end::Modal title-->
                    <!--begin::Close-->
                    <div id="kt_modal_add_customer_close" class="btn btn-icon btn-sm btn-active-icon-primary">
                        <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
                        <span class="svg-icon svg-icon-1">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.5" x="6" y="17.3137" width="16" height="2" rx="1" transform="rotate(-45 6 17.3137)" fill="currentColor" />
                                <rect x="7.41422" y="6" width="16" height="2" rx="1" transform="rotate(45 7.41422 6)" fill="currentColor" />
                            </svg>
                        </span>
                        <!--end::Svg Icon-->
                    </div>
                    <!--end::Close-->
                </div>
                <!--end::Modal header-->
                <!--begin::Modal body-->
                <div class="modal-body py-10 px-lg-17">
                    <!--begin::Scroll-->
                    <div class="scroll-y me-n7 pe-7" id="kt_modal_add_customer_scroll" data-kt-scroll="true" data-kt-scroll-activate="{default: false, lg: true}" data-kt-scroll-max-height="auto" data-kt-scroll-dependencies="#kt_modal_add_customer_header" data-kt-scroll-wrappers="#kt_modal_add_customer_scroll" data-kt-scroll-offset="300px">
                        <!--begin::Input group-->

                                <!--begin::Input group-->
                                <div class="row mb-6">
                                    <!--begin::Label-->
                                    <label class="col-lg-4 col-form-label fw-bold fs-6">
                                        <span class="required">@lang('dashboard.delegates')</span>
                                        <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip"
                                            title="" data-bs-original-title="Delegate of origination"
                                            aria-label="Delegate of origination"></i>
                                    </label>
                                    <!--end::Label-->
                                    <!--begin::Col-->
                                    <div class="col-lg-8 fv-row fv-plugins-icon-container">
                                        <select name="delegate_id" aria-label="Select a Delegate" data-control="select2"
                                            data-placeholder="@lang('dashboard.select_delegate')"
                                            class="form-select form-select-solid form-select-lg fw-bold select2-hidden-accessible"
                                            data-select2-id="select2-data-10-05ls" tabindex="-1" aria-hidden="true">
                                            <option value="" data-select2-id="select2-data-12-wa4o">
                                                @lang('dashboard.select_delegate')</option>
                                            @foreach ($delegates as $delegate)
                                                <option value="{{ $delegate->id }}">
                                                    {{$delegate->name}}</option>
                                            @endforeach
                                        </select>
                                        <div class="fv-plugins-message-container invalid-feedback"></div>
                                    </div>
                                    <!--end::Col-->
                                </div>
                                <!--end::Input group-->



                    </div>
                    <!--end::Scroll-->
                </div>
                <!--end::Modal body-->
                <!--begin::Modal footer-->
                <div class="modal-footer flex-center">
                    <!--begin::Button-->
                    <button type="reset" id="kt_modal_add_customer_cancel" class="btn btn-light me-3">@lang('dashboard.cancel')</button>
                    <!--end::Button-->
                    <!--begin::Button-->
                    <button type="submit" id="kt_modal_add_customer_submit" class="btn btn-primary send-notify-button">
                        <span class="indicator-label">@lang('dashboard.save_changes')</span>
                        <span class="indicator-progress">@lang('dashboard.please_wait')
                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                    </button>
                    <!--end::Button-->
                </div>
                <!--end::Modal footer-->
            </form>
            <!--end::Form-->
        </div>
    </div>
</div>
<!--end::Modal - Customers - Add-->


<div class="post  flex-column-fluid" id="kt_post">
        <!--begin::Container-->
        <div id="kt_content_container" class="container-xxl">
            <div data-kt-swapper="true" data-kt-swapper-mode="prepend"
                data-kt-swapper-parent="{default: '#kt_content_container', 'lg': '#kt_toolbar_container'}"
                class="page-title  align-items-center flex-wrap me-3 mb-5 mb-lg-0">
                <!--begin::Title-->
                <h1 class=" text-dark fw-bolder fs-3 align-items-center my-1">@lang('dashboard.orders')</h1>
                <!--end::Title-->
                <!--begin::Separator-->
                <span class="h-20px border-gray-300 border-start mx-4"></span>
                <!--end::Separator-->
                <!--begin::Breadcrumb-->
                <ul class="breadcrumb breadcrumb-separatorless fw-bold fs-7 my-1">
                    <!--begin::Item-->
                    <li class="breadcrumb-item text-muted">
                        <a href="{{ route('home') }}" class="text-muted text-hover-primary">@lang('dashboard.home')</a>
                    </li>
                    <!--end::Item-->

                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-300 w-5px h-2px"></span>
                    </li>

                    <!--end::Item-->
                    <!--begin::Item-->
                    <li class="breadcrumb-item text-dark">@lang('dashboard.orders')</li>
                    <!--end::Item-->
                </ul>
                <!--end::Breadcrumb-->
            </div>

            <!--begin::Statistics Cards-->
            <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                <!--begin::Col-->
                <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                    <!--begin::Card widget 20-->
                    <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50px">
                        <!--begin::Header-->
                        <div class="card-header pt-5">
                            <!--begin::Title-->
                            <div class="card-title d-flex flex-column">
                                <!--begin::Amount-->
                                <div class="d-flex align-items-center">
                                    <span class="fs-4 fw-semibold text-gray-400 me-1 align-self-start">{{ $stats['total_orders'] ?? $orders->total() }}</span>
                                </div>
                                <!--end::Amount-->
                                <!--begin::Subtitle-->
                                <span class="text-gray-400 pt-1 fw-semibold fs-6">إجمالي الطلبات</span>
                                <!--end::Subtitle-->
                            </div>
                            <!--end::Title-->
                        </div>
                        <!--end::Header-->
                    </div>
                    <!--end::Card widget 20-->
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                    <!--begin::Card widget 20-->
                    <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50px">
                        <!--begin::Header-->
                        <div class="card-header pt-5">
                            <!--begin::Title-->
                            <div class="card-title d-flex flex-column">
                                <!--begin::Amount-->
                                <div class="d-flex align-items-center">
                                    <span class="fs-4 fw-semibold text-warning me-1 align-self-start">{{ $stats['pending_orders'] ?? 0 }}</span>
                                </div>
                                <!--end::Amount-->
                                <!--begin::Subtitle-->
                                <span class="text-gray-400 pt-1 fw-semibold fs-6">طلبات معلقة</span>
                                <!--end::Subtitle-->
                            </div>
                            <!--end::Title-->
                        </div>
                        <!--end::Header-->
                    </div>
                    <!--end::Card widget 20-->
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                    <!--begin::Card widget 20-->
                    <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50px">
                        <!--begin::Header-->
                        <div class="card-header pt-5">
                            <!--begin::Title-->
                            <div class="card-title d-flex flex-column">
                                <!--begin::Amount-->
                                <div class="d-flex align-items-center">
                                    <span class="fs-4 fw-semibold text-success me-1 align-self-start">{{ $stats['completed_orders'] ?? 0 }}</span>
                                </div>
                                <!--end::Amount-->
                                <!--begin::Subtitle-->
                                <span class="text-gray-400 pt-1 fw-semibold fs-6">طلبات مكتملة</span>
                                <!--end::Subtitle-->
                            </div>
                            <!--end::Title-->
                        </div>
                        <!--end::Header-->
                    </div>
                    <!--end::Card widget 20-->
                </div>
                <!--end::Col-->

                <!--begin::Col-->
                <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                    <!--begin::Card widget 20-->
                    <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50px">
                        <!--begin::Header-->
                        <div class="card-header pt-5">
                            <!--begin::Title-->
                            <div class="card-title d-flex flex-column">
                                <!--begin::Amount-->
                                <div class="d-flex align-items-center">
                                    <span class="fs-4 fw-semibold text-danger me-1 align-self-start">{{ $stats['cancelled_orders'] ?? 0 }}</span>
                                </div>
                                <!--end::Amount-->
                                <!--begin::Subtitle-->
                                <span class="text-gray-400 pt-1 fw-semibold fs-6">طلبات ملغية</span>
                                <!--end::Subtitle-->
                            </div>
                            <!--end::Title-->
                        </div>
                        <!--end::Header-->
                    </div>
                    <!--end::Card widget 20-->
                </div>
                <!--end::Col-->
            </div>
            <!--end::Statistics Cards-->

            <!--begin::Products-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold fs-3 mb-1">إدارة الطلبات</span>
                            <span class="text-muted mt-1 fw-semibold fs-7">{{ $orders->total() }} طلب إجمالي</span>
                        </h3>
                    </div>
                    <!--end::Card title-->
                    <!--begin::Card toolbar-->
                    <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
                        <!--begin::Flatpickr-->
                        <div class="input-group w-250px">
                            <input class="form-control form-control-solid rounded rounded-end-0 flatpickr-input"
                                placeholder="اختر متوسط الوقت" id="kt_ecommerce_sales_flatpickr" type="hidden">
                            {{-- <input class="form-control form-control-solid rounded rounded-end-0 form-control input" placeholder="Pick date range" tabindex="0" type="text" readonly="readonly"> --}}
                            <button class="btn btn-icon btn-light" id="kt_ecommerce_sales_flatpickr_clear">
                                <!--begin::Svg Icon | path: icons/duotune/arrows/arr088.svg-->
                                <span class="svg-icon svg-icon-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none">
                                        <rect opacity="0.5" x="7.05025" y="15.5356" width="12" height="2"
                                            rx="1" transform="rotate(-45 7.05025 15.5356)" fill="currentColor">
                                        </rect>
                                        <rect x="8.46447" y="7.05029" width="12" height="2" rx="1"
                                            transform="rotate(45 8.46447 7.05029)" fill="currentColor"></rect>
                                    </svg>
                                </span>
                                <!--end::Svg Icon-->
                            </button>
                        </div>
                        <!--end::Flatpickr-->
                        <div class="w-100 mw-150px">
                            <!--begin::Select2-->
                            <select class="form-select form-select-solid select2-hidden-accessible" data-control="select2"
                                data-hide-search="true" data-placeholder="الحالة" data-kt-ecommerce-order-filter="status"
                                data-select2-id="select2-data-10-pnkc" tabindex="-1" aria-hidden="true">
                                <option data-select2-id="select2-data-12-5axm"></option>
                                <option value="all">الكل</option>
                                <option value="Cancelled">الملغية</option>
                                <option value="Completed">المكتملة</option>
                                <option value="Denied">المرفوضة</option>
                                <option value="Expired">منتهية</option>
                                <option value="Failed">فشلت</option>
                                <option value="Pending">المنتظرة</option>
                                <option value="Processing">جاري العمل</option>
                                <option value="Refunded">مرتجع</option>
                                <option value="Delivered">المنتهية</option>
                                <option value="Delivering">جاري العمل</option>
                            </select>
                            <!--end::Select2-->
                        </div>

                        <!--filter payment_type  -->
                        <div class="w-100 mw-150px">
                            <!--begin::Select2-->
                            <select class="form-select form-select-solid select2-hidden-accessible" data-control="select2"
                                data-hide-search="true" data-placeholder="نوع الدفع" data-kt-ecommerce-order-filter="payment_type"
                                data-select2-id="select2-data-11-pnkc" tabindex="-1" aria-hidden="true">
                                <option data-select2-id="select2-data-12-5axm"></option>
                                <option value="all">الكل</option>
                                <option value="cash">نقدي</option>
                                <option value="visa">فيزا</option>
                                <option value="mada">مدى</option>
                                <option value="mastercard">ماستر كارد</option>
                                <option value="applepay">ابل باي</option>
                            </select>
                            <!--end::Select2-->
                        </div>

                    </div>
                    <!--end::Card toolbar-->

                    <div class="card-toolbar">
                        <!--begin::Add customer-->
                        <a href="{{ route('orders.create')}}" class="btn btn-primary">@lang('dashboard.create_title', ['page_title' => __('dashboard.user_order')])</a>
                        <!--end::Add customer-->
                        <span class="w-5px h-2px"></span>

                    </div>
                </div>
                <!--end::Card header-->

                <!--begin::Filter Form-->
                <div class="card-body border-top pt-6">
                    <form method="GET" action="{{ route('orders.index') }}" class="d-flex flex-wrap align-items-center gap-3">
                        <!--begin::Search-->
                        <div class="position-relative">
                            <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-3 top-50 translate-middle-y">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            <input type="text" name="search" value="{{ request('search') }}"
                                   class="form-control form-control-solid w-250px ps-10"
                                   placeholder="ابحث عن طلب أو عميل...">
                        </div>
                        <!--end::Search-->

                        <!--begin::Status Filter-->
                        <select name="status" class="form-select form-select-solid w-150px">
                            <option value="">كل الحالات</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>معلقة</option>
                            <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>قيد المعالجة</option>
                            <option value="done" {{ request('status') == 'done' ? 'selected' : '' }}>مكتملة</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>ملغية</option>
                            <option value="delivering" {{ request('status') == 'delivering' ? 'selected' : '' }}>جاري التوصيل</option>
                        </select>
                        <!--end::Status Filter-->

                        <!--begin::Payment Type Filter-->
                        <select name="payment_type" class="form-select form-select-solid w-150px">
                            <option value="">كل أنواع الدفع</option>
                            <option value="cash" {{ request('payment_type') == 'cash' ? 'selected' : '' }}>نقدي</option>
                            <option value="visa" {{ request('payment_type') == 'visa' ? 'selected' : '' }}>فيزا</option>
                            <option value="online" {{ request('payment_type') == 'online' ? 'selected' : '' }}>أونلاين</option>
                        </select>
                        <!--end::Payment Type Filter-->

                        <!--begin::Delegate Filter-->
                        <select name="delegate_id" class="form-select form-select-solid w-150px">
                            <option value="">كل المندوبين</option>
                            @foreach($delegates as $delegate)
                                <option value="{{ $delegate->id }}" {{ request('delegate_id') == $delegate->id ? 'selected' : '' }}>
                                    {{ $delegate->name }}
                                </option>
                            @endforeach
                        </select>
                        <!--end::Delegate Filter-->

                        <!--begin::Date From-->
                        <input type="date" name="date_from" value="{{ request('date_from') }}"
                               class="form-control form-control-solid w-150px" placeholder="من تاريخ">
                        <!--end::Date From-->

                        <!--begin::Date To-->
                        <input type="date" name="date_to" value="{{ request('date_to') }}"
                               class="form-control form-control-solid w-150px" placeholder="إلى تاريخ">
                        <!--end::Date To-->

                        <!--begin::Filter Button-->
                        <button type="submit" class="btn btn-primary">
                            <i class="ki-duotone ki-filter fs-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            تصفية
                        </button>
                        <!--end::Filter Button-->

                        <!--begin::Reset Button-->
                        <a href="{{ route('orders.index') }}" class="btn btn-light">
                            <i class="ki-duotone ki-arrows-circle fs-2">
                                <span class="path1"></span>
                                <span class="path2"></span>
                            </i>
                            إعادة تعيين
                        </a>
                        <!--end::Reset Button-->
                    </form>
                </div>
                <!--end::Filter Form-->

                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <!--begin::Table-->
                    <div class="table-responsive">
                        <table class="table align-middle table-row-dashed fs-6 gy-5"
                            id="orders_table">
                                <!--begin::Table head-->
                                <thead>
                                    <!--begin::Table row-->
                                    <tr class="text-center text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
                                        <th class="w-10px pe-2 sorting_disabled" rowspan="1" colspan="1"
                                            aria-label="" style="width: 29.25px;">
                                            <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                                <input class="form-check-input" type="checkbox" data-kt-check="true"
                                                    data-kt-check-target="#kt_ecommerce_sales_table .form-check-input"
                                                    value="1">
                                            </div>
                                        </th>
                                        <th class="min-w-100px sorting" tabindex="0"
                                            aria-controls="kt_ecommerce_sales_table" rowspan="1" colspan="1"
                                            aria-label="Order ID: activate to sort column ascending"
                                            style="width: 111.062px;">رقم الطلب</th>
                                        <th class="min-w-175px sorting" tabindex="0"
                                            aria-controls="kt_ecommerce_sales_table" rowspan="1" colspan="1"
                                            aria-label="Customer: activate to sort column ascending"
                                            style="width:  111.062px;">العميل</th>
                                        <th class=" min-w-70px sorting" tabindex="0"
                                            aria-controls="kt_ecommerce_sales_table" rowspan="1" colspan="1"
                                            aria-label="Status: activate to sort column ascending"
                                            style="width: 80.875px;">حالة الطلب ونوع الدفع</th>
                                        <th class="  min-w-175px sorting" tabindex="0"
                                            aria-controls="kt_ecommerce_sales_table" rowspan="1" colspan="1"
                                            aria-label="Delegate: activate to sort column ascending"
                                            style="width:  111.062px;">المندوب</th>
                                        <th class=" min-w-100px sorting" tabindex="0"
                                            aria-controls="kt_ecommerce_sales_table" rowspan="1" colspan="1"
                                            aria-label="Total: activate to sort column ascending"
                                            style="width: 111.062px;">الإجمالي</th>
                                        <th class=" min-w-100px sorting" tabindex="0"
                                            aria-controls="kt_ecommerce_sales_table" rowspan="1" colspan="1"
                                            aria-label="Date Added: activate to sort column ascending"
                                            style="width: 111.062px;">تاريخ الطلب</th>
                                        <th class=" min-w-100px sorting" tabindex="0"
                                            aria-controls="kt_ecommerce_sales_table" rowspan="1" colspan="1"
                                            aria-label="Date Modified: activate to sort column ascending"
                                            style="width: 111.062px;">وقت الطلب</th>
                                            <th class=" min-w-100px sorting" tabindex="0"
                                            aria-controls="kt_ecommerce_sales_table" rowspan="1" colspan="1"
                                            aria-label="Date Modified: activate to sort column ascending"
                                            style="width: 111.062px;"> نوع الغسله</th>
                                        <th class=" min-w-100px sorting_disabled" rowspan="1" colspan="1"
                                            aria-label="Actions" style="width: 111.109px;">المهام</th>
                                    </tr>
                                    <!--end::Table row-->
                                </thead>
                                <!--end::Table head-->
                                <!--begin::Table body-->
                                <tbody class="text-center fw-bold text-gray-600">

                                    @foreach ($orders as $order)
                                        <tr class="{{ $loop->iteration % 2 == 0 ? 'even' : 'odd' }}">
                                            <!--begin::Checkbox-->
                                            <td>
                                                <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                    <input class="form-check-input" type="checkbox" value="1">
                                                </div>
                                            </td>
                                            <!--end::Checkbox-->
                                            <!--begin::Order ID=-->
                                            <td data-kt-ecommerce-order-filter="order_id">
                                                <a href="{{ route('orders.show', $order->id) }}"
                                                    class="text-gray-800 text-hover-primary fw-bolder">{{ $order->order_num }}</a>
                                            </td>
                                            <!--end::Order ID=-->
                                            <!--begin::Customer=-->
                                            <td>
                                                <div class=" align-items-center">
                                                    <div class=" pe-0">
                                                        <!--begin::Title-->
                                                        <span
                                                            class="text-gray-800 text-hover-primary fs-5 fw-bolder">{{ $order->user->name }}</span>
                                                        <!--end::Title-->
                                                    </div>
                                                </div>
                                            </td>
                                            <!--end::Customer=-->
                                            <!--begin::Status=-->
                                            <td class=" pe-0" data-order="{{ $order->status }}">

                                                <!--begin::Badges-->
                                                <div style="">{{ $order->payment_url }}</div>

                                                <div style="display: none">{{ $order->status }}</div>
                                                <div style="display: none">{{ $order->payment_type }}</div>
                                                <div
                                                    class="badge  {{ $order->status == 'cancelled' ? ' badge-light-danger' : ' badge-light-success' }} ">
                                                    {{ $order->status_name }}</div>
                                                <div class="PaymentTypeClass">نوع الدفع <span>{{ $order->payment_type }}</span></div>

                                                <style>
                                                    .PaymentTypeClass span {
                                                        display: inline-block;
                                                        background: #e10000;
                                                        padding: 2px 5px;
                                                    }
                                                    .PaymentTypeClass {
                                                        background: #000;
                                                        color: #fff;
                                                        padding: 2px 5px;
                                                        border-radius: 3px;
                                                        line-height: 1;
                                                        margin-top: 5px;
                                                    }
                                                </style>
                                                <!--end::Badges-->
                                            </td>
                                            <!--end::Status=-->

                                            <td>
                                                <div class=" pe-0">
                                                    <!--begin::Title-->
                                                    <span
                                                        class=" fw-bolder">{{ $order->delegate ? $order->delegate->name : __('dashboard.not_selected_yet') }}</span>
                                                    <!--end::Title-->
                                                </div>
                                            </td>
                                            <!--begin::Total=-->
                                            <td class=" pe-0">
                                                <span
                                                    class="fw-bolder">{{ $order->final_cost . ' ' . __('dashboard.RS') }}</span>
                                            </td>
                                            <!--end::Total=-->
                                            <!--begin::Date Added=-->
                                            <td class="" data-order="{{ $order->created_at->format('Y-m-d') }}">
                                                <span class="fw-bolder">{{ $order->created_at->format('d/m/Y') }}</span>
                                            </td>
                                            <!--end::Date Added=-->
                                            <!--begin::Date Modified=-->
                                            <td class="" data-order="{{ $order->created_at->format('Y-m-d') }}">
                                                <span class="fw-bolder">{{ $order->created_at->format('H:i:s') }}</span>
                                            </td>
                                            <td class="" data-order="{{ $order?->scheduled_at }}">
                                                <span class="fw-bolder">{{ $order?->scheduled_at ?? 'غير مجدوله' }}</span>
                                            </td>
                                            <!--end::Date Modified=-->
                                            <!--begin::Action=-->
                                            <td class="">
                                                <a href="#" class="btn btn-sm btn-light btn-active-light-primary"
                                                    data-kt-menu-trigger="click"
                                                    data-kt-menu-placement="bottom-end">@lang('dashboard.actionsBtn')
                                                    <!--begin::Svg Icon | path: icons/duotune/arrows/arr072.svg-->
                                                    <span class="svg-icon svg-icon-5 m-0">
                                                        <svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                            height="24" viewBox="0 0 24 24" fill="none">
                                                            <path
                                                                d="M11.4343 12.7344L7.25 8.55005C6.83579 8.13583 6.16421 8.13584 5.75 8.55005C5.33579 8.96426 5.33579 9.63583 5.75 10.05L11.2929 15.5929C11.6834 15.9835 12.3166 15.9835 12.7071 15.5929L18.25 10.05C18.6642 9.63584 18.6642 8.96426 18.25 8.55005C17.8358 8.13584 17.1642 8.13584 16.75 8.55005L12.5657 12.7344C12.2533 13.0468 11.7467 13.0468 11.4343 12.7344Z"
                                                                fill="currentColor"></path>
                                                        </svg>
                                                    </span>
                                                    <!--end::Svg Icon-->
                                                </a>
                                                <!--begin::Menu-->
                                                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-bold fs-7 w-125px py-4"
                                                    data-kt-menu="true">
                                                    <!--begin::Menu item-->
                                                    <div class="menu-item px-3">
                                                        <a href="{{ route('orders.show', $order->id) }}"
                                                            class="menu-link px-3">@lang('actions.show')</a>
                                                    </div>
                                                    <!--end::Menu item-->
                                                    <!--begin::Menu item-->
                                                    <div class="menu-item px-3">
                                                        <a href="{{ route('orders.edit', $order->id) }}"
                                                            class="menu-link px-3">@lang('actions.edit')</a>
                                                    </div>
                                                    <!--end::Menu item-->
                                                    <div class="menu-item px-3">
                                                        <a href="#" class="menu-link px-3 assign_order"  data-id="{{ $order->id }}"
                                                            data-bs-toggle="modal" data-bs-target="#kt_modal_add_customer">
                                                            @lang('dashboard.assign_order')
                                                        </a>
                                                    </div>
                                                    <!--begin::Menu item-->
                                                    <div class="menu-item px-3">
                                                        <a href="#" class="menu-link px-3"
                                                            data-url="{{ route('orders.destroy', $order->id) }}"
                                                            data-id="{{ $order->id }}"
                                                            data-kt-ecommerce-order-filter="delete_row">@lang('actions.delete')</a>
                                                    </div>
                                                    <div class="menu-item px-3">

                                                    <button class=" btn btn-sm btn-primary generate-payment-link"
                                                    data-id="{{ $order->id }}">
                                                @lang('dashboard.generate_payment_link')
                                            </button>
                                                    </div>
                                                    <!--end::Menu item-->
                                                </div>
                                                <!--end::Menu-->

                                            </td>
                                            <!--end::Action=-->

                                        </tr>
                                    @endforeach
                                </tbody>
                                <!--end::Table body-->
                            </table>
                        </div>
                    </div>
                    <!--end::Table-->

                    <!--begin::Pagination-->
                    <div class="row">
                        <div class="col-sm-12 col-md-5 d-flex align-items-center justify-content-center justify-content-md-start">
                            <div class="dataTables_info" role="status" aria-live="polite">
                                عرض {{ $orders->firstItem() }} إلى {{ $orders->lastItem() }} من {{ $orders->total() }} طلب
                            </div>
                        </div>
                        <div class="col-sm-12 col-md-7 d-flex align-items-center justify-content-center justify-content-md-end">
                            <div class="dataTables_paginate paging_simple_numbers">
                                {{ $orders->links('pagination::bootstrap-4') }}
                            </div>
                        </div>
                    </div>
                    <!--end::Pagination-->
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Products-->
        </div>
        <!--end::Container-->
</div>
@endsection
@section('scripts')


<script>
    $(document).on('click' , '.assign_order' , function (e) {
        $('.order_id').val($(this).data('id'))
    })
</script>

<script>
document.addEventListener("DOMContentLoaded", function () {
    // 🛑 استخدم event delegation لضمان عمل الزر حتى عند تغيير الصفحات
    document.addEventListener("click", function (e) {
        if (e.target.classList.contains("generate-payment-link")) {
            e.preventDefault();

            let button = e.target;
            let orderId = button.getAttribute("data-id");

            // 🛑 Confirm action with the user
            if (!confirm("هل أنت متأكد من إنشاء رابط الدفع؟")) {
                return;
            }

            // 🌀 Show loading indicator
            let originalText = button.innerHTML;
            button.innerHTML = "جاري الإنشاء...";
            button.disabled = true;

            // 📨 Send AJAX request to generate payment link
            fetch(`/orders/${orderId}/paymentLink`, {
                method: "PUT",
                headers: {
                    "X-CSRF-TOKEN": document.querySelector('meta[name="csrf-token"]').getAttribute("content"),
                    "X-Requested-With": "XMLHttpRequest",
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                },
                credentials: "same-origin"
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.payment_url) {
                    // ✅ Show the payment link
                    alert(`🎉 رابط الدفع تم إنشاؤه بنجاح: \n ${data.payment_url}`);

                    // 🔗 Optional: Copy link to clipboard
                    navigator.clipboard.writeText(data.payment_url)
                        .then(() => alert("📋 تم نسخ رابط الدفع إلى الحافظة"))
                        .catch(err => console.error("❌ فشل نسخ الرابط: ", err));

                    // ✅ تحديث الرابط في الجدول تلقائيًا
                    let orderRow = button.closest("tr");
                    let paymentCell = orderRow.querySelector("[data-payment-url]");
                    if (paymentCell) {
                        paymentCell.innerHTML = `<a href="${data.payment_url}" target="_blank">${data.payment_url}</a>`;
                    }
                } else {
                    throw new Error("❌ لم يتم العثور على رابط الدفع في الاستجابة!");
                }
            })
            .catch(error => {
                alert(`⚠️ خطأ أثناء إنشاء رابط الدفع: ${error.message}`);
                console.error("❌ Payment Link Error:", error);
            })
            .finally(() => {
                // 🔄 Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        }
    });
});


</script>

<script src="{{asset('dashboard/custom/js/modal.js')}}"></script>

@endsection
