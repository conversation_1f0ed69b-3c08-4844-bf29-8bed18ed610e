<div class="d-flex justify-content-end flex-shrink-0">
    <!--begin::Menu-->
    <a href="#" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
        <!--begin::Svg Icon | path: icons/duotune/general/gen052.svg-->
        <span class="svg-icon svg-icon-3">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="10" y="10" width="4" height="4" rx="2" fill="currentColor"></rect>
                <rect x="17" y="10" width="4" height="4" rx="2" fill="currentColor"></rect>
                <rect x="3" y="10" width="4" height="4" rx="2" fill="currentColor"></rect>
            </svg>
        </span>
        <!--end::Svg Icon-->
    </a>
    <!--begin::Menu 3-->
    <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-semibold w-200px py-3" data-kt-menu="true">
        <!--begin::Heading-->
        <div class="menu-item px-3">
            <div class="menu-content text-muted pb-2 px-3 fs-7 text-uppercase">الإجراءات</div>
        </div>
        <!--end::Heading-->
        
        <!--begin::Menu item-->
        <div class="menu-item px-3">
            <a href="{{ route('orders.show', $order->id) }}" class="menu-link px-3">
                <span class="svg-icon svg-icon-3 me-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M17.5 11H6.5C4 11 2 9 2 6.5C2 4 4 2 6.5 2H17.5C20 2 22 4 22 6.5C22 9 20 11 17.5 11ZM15 6.5C15 7.9 16.1 9 17.5 9C18.9 9 20 7.9 20 6.5C20 5.1 18.9 4 17.5 4C16.1 4 15 5.1 15 6.5Z" fill="currentColor"></path>
                        <path opacity="0.3" d="M17.5 22H6.5C4 22 2 20 2 17.5C2 15 4 13 6.5 13H17.5C20 13 22 15 22 17.5C22 20 20 22 17.5 22ZM4 17.5C4 18.9 5.1 20 6.5 20C7.9 20 9 18.9 9 17.5C9 16.1 7.9 15 6.5 15C5.1 15 4 16.1 4 17.5Z" fill="currentColor"></path>
                    </svg>
                </span>
                عرض التفاصيل
            </a>
        </div>
        <!--end::Menu item-->
        
        <!--begin::Menu item-->
        <div class="menu-item px-3">
            <a href="{{ route('orders.edit', $order->id) }}" class="menu-link px-3">
                <span class="svg-icon svg-icon-3 me-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M21.4 8.35303L19.241 10.511L13.485 4.755L15.643 2.59595C16.0248 2.21423 16.5426 1.99988 17.0825 1.99988C17.6224 1.99988 18.1402 2.21423 18.522 2.59595L21.4 5.474C21.7817 5.85581 21.9962 6.37355 21.9962 6.91345C21.9962 7.45335 21.7817 7.97122 21.4 8.35303ZM3.68699 21.932L9.88699 19.865L4.13699 14.115L2.06999 20.315C1.98815 20.5619 1.99703 20.8267 2.09478 21.0681C2.19253 21.3095 2.36823 21.5130 2.59699 21.6493C2.82575 21.7856 3.09262 21.8470 3.35999 21.8252C3.62736 21.8034 3.88141 21.6995 4.08699 21.5252L3.68699 21.932Z" fill="currentColor"></path>
                        <path d="M5.574 21.3L3.692 21.928C3.46591 22.0032 3.22334 22.0141 2.99144 21.9594C2.75954 21.9046 2.54744 21.7864 2.3789 21.6179C2.21036 21.4495 2.09202 21.2375 2.03711 21.0056C1.9822 20.7737 1.99289 20.5312 2.06799 20.3051L2.696 18.422L5.574 21.3Z" fill="currentColor"></path>
                        <path d="M20.8 10.8L9.912 21.688C9.8694 21.7306 9.8180 21.7632 9.76165 21.7832C9.70531 21.8031 9.64497 21.8099 9.585 21.8031L4.105 21.0031C4.01264 20.9897 3.92533 20.9511 3.85298 20.8919C3.78063 20.8328 3.72597 20.7554 3.695 20.6681L2.895 15.1881C2.88813 15.1281 2.89491 15.0678 2.91462 15.0114C2.93433 14.955 2.96646 14.9036 3.009 14.8611L13.897 3.97302C14.3897 4.42302 14.9297 4.84302 15.4897 5.24302C16.0497 5.64302 16.6297 6.01302 17.2297 6.35302C17.8297 6.69302 18.4497 6.99302 19.0897 7.25302C19.7297 7.51302 20.3897 7.73302 21.0697 7.91302L20.8 10.8Z" fill="currentColor"></path>
                    </svg>
                </span>
                تعديل
            </a>
        </div>
        <!--end::Menu item-->
        
        @if($order->status === 'pending')
        <!--begin::Menu item-->
        <div class="menu-item px-3">
            <a href="#" class="menu-link px-3" onclick="assignDelegate({{ $order->id }})">
                <span class="svg-icon svg-icon-3 me-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.3" d="M20 3H4C2.89543 3 2 3.89543 2 5V16C2 17.1046 2.89543 18 4 18H4.5C5.05228 18 5.5 18.4477 5.5 19V21.5052C5.5 22.1441 6.21212 22.5253 6.74376 22.1708L11.4885 19.0077C12.4741 18.3506 13.6321 18 14.8167 18H20C21.1046 18 22 17.1046 22 16V5C22 3.89543 21.1046 3 20 3Z" fill="currentColor"></path>
                        <rect x="6" y="12" width="7" height="2" rx="1" fill="currentColor"></rect>
                        <rect x="6" y="7" width="12" height="2" rx="1" fill="currentColor"></rect>
                    </svg>
                </span>
                تعيين مندوب
            </a>
        </div>
        <!--end::Menu item-->
        @endif
        
        <!--begin::Menu separator-->
        <div class="separator mt-3 opacity-75"></div>
        <!--end::Menu separator-->
        
        <!--begin::Menu item-->
        <div class="menu-item px-3">
            <a href="#" class="menu-link px-3 text-danger" onclick="deleteOrder({{ $order->id }})">
                <span class="svg-icon svg-icon-3 me-2">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5 9C5 8.44772 5.44772 8 6 8H18C18.5523 8 19 8.44772 19 9V18C19 19.6569 17.6569 21 16 21H8C6.34315 21 5 19.6569 5 18V9Z" fill="currentColor"></path>
                        <path opacity="0.5" d="M5 5C5 4.44772 5.44772 4 6 4H18C18.5523 4 19 4.44772 19 5V5C19 5.55228 18.5523 6 18 6H6C5.44772 6 5 5.55228 5 5V5Z" fill="currentColor"></path>
                        <path opacity="0.5" d="M9 4C9 3.44772 9.44772 3 10 3H14C14.5523 3 15 3.44772 15 4V4H9V4Z" fill="currentColor"></path>
                    </svg>
                </span>
                حذف
            </a>
        </div>
        <!--end::Menu item-->
    </div>
    <!--end::Menu 3-->
    <!--end::Menu-->
</div>

<script>
function assignDelegate(orderId) {
    // Implement delegate assignment functionality
    Swal.fire({
        title: 'تعيين مندوب',
        text: 'هل تريد تعيين مندوب لهذا الطلب؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'نعم',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // Make AJAX call to assign delegate
            $.ajax({
                url: '/dashboard/orders/' + orderId + '/assign-delegate',
                method: 'POST',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    Swal.fire('تم!', 'تم تعيين المندوب بنجاح', 'success');
                    $('#orders-table').DataTable().ajax.reload();
                },
                error: function() {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء تعيين المندوب', 'error');
                }
            });
        }
    });
}

function deleteOrder(orderId) {
    Swal.fire({
        title: 'هل أنت متأكد؟',
        text: 'لن تتمكن من التراجع عن هذا الإجراء!',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف!',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: '/dashboard/orders/' + orderId,
                method: 'DELETE',
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    Swal.fire('تم الحذف!', 'تم حذف الطلب بنجاح', 'success');
                    $('#orders-table').DataTable().ajax.reload();
                },
                error: function() {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء حذف الطلب', 'error');
                }
            });
        }
    });
}
</script>
