@extends('dashboard.layouts.app')

@section('title', __('dashboard.orders'))

@section('content')
<!--begin::Content-->
<div id="kt_app_content" class="app-content flex-column-fluid">
    <!--begin::Content container-->
    <div id="kt_app_content_container" class="app-container container-xxl">
        
        <!-- Performance Statistics Cards -->
        <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
            <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50px">
                    <div class="card-header pt-5">
                        <div class="card-title d-flex flex-column">
                            <div class="d-flex align-items-center">
                                <span class="fs-4 fw-semibold text-gray-400 me-1 align-self-start">{{ $stats['total_orders'] ?? 0 }}</span>
                            </div>
                            <span class="text-gray-400 pt-1 fw-semibold fs-6">إجمالي الطلبات</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50px">
                    <div class="card-header pt-5">
                        <div class="card-title d-flex flex-column">
                            <div class="d-flex align-items-center">
                                <span class="fs-4 fw-semibold text-warning me-1 align-self-start">{{ $stats['pending_orders'] ?? 0 }}</span>
                            </div>
                            <span class="text-gray-400 pt-1 fw-semibold fs-6">طلبات معلقة</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50px">
                    <div class="card-header pt-5">
                        <div class="card-title d-flex flex-column">
                            <div class="d-flex align-items-center">
                                <span class="fs-4 fw-semibold text-success me-1 align-self-start">{{ $stats['completed_orders'] ?? 0 }}</span>
                            </div>
                            <span class="text-gray-400 pt-1 fw-semibold fs-6">طلبات مكتملة</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6 col-lg-6 col-xl-6 col-xxl-3 mb-md-5 mb-xl-10">
                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-md-50px">
                    <div class="card-header pt-5">
                        <div class="card-title d-flex flex-column">
                            <div class="d-flex align-items-center">
                                <span class="fs-4 fw-semibold text-danger me-1 align-self-start">{{ $stats['cancelled_orders'] ?? 0 }}</span>
                            </div>
                            <span class="text-gray-400 pt-1 fw-semibold fs-6">طلبات ملغية</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--begin::Card-->
        <div class="card">
            <!--begin::Card header-->
            <div class="card-header border-0 pt-6">
                <!--begin::Card title-->
                <div class="card-title">
                    <!--begin::Search-->
                    <div class="d-flex align-items-center position-relative my-1">
                        <span class="svg-icon svg-icon-1 position-absolute ms-6">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor"></rect>
                                <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor"></path>
                            </svg>
                        </span>
                        <input type="text" id="orders-search" class="form-control form-control-solid w-250px ps-14" placeholder="ابحث عن طلب...">
                    </div>
                    <!--end::Search-->
                </div>
                <!--end::Card title-->
                
                <!--begin::Card toolbar-->
                <div class="card-toolbar flex-row-fluid justify-content-end gap-5">
                    <!--begin::Status Filter-->
                    <select class="form-select form-select-solid w-150px" id="status-filter">
                        <option value="">جميع الحالات</option>
                        <option value="pending">معلق</option>
                        <option value="preparing">قيد التحضير</option>
                        <option value="done">مكتمل</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                    <!--end::Status Filter-->
                    
                    <!--begin::Date Filter-->
                    <input class="form-control form-control-solid w-200px" placeholder="اختر نطاق التاريخ" id="date-filter">
                    <!--end::Date Filter-->
                    
                    <!--begin::Export-->
                    <button type="button" class="btn btn-light-primary" id="export-orders">
                        <span class="svg-icon svg-icon-2">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path opacity="0.3" d="M19 15C20.7 15 22 13.7 22 12C22 10.3 20.7 9 19 9C18.9 9 18.9 9 18.8 9C18.9 8.7 19 8.3 19 8C19 6.3 17.7 5 16 5C15.4 5 14.8 5.2 14.3 5.5C13.4 4 11.8 3 10 3C7.2 3 5 5.2 5 8C5 8.3 5 8.7 5.1 9C5.1 9 5.1 9 5 9C3.3 9 2 10.3 2 12C2 13.7 3.3 15 5 15H19Z" fill="currentColor"></path>
                                <path d="M13 17.4V12C13 11.4 12.6 11 12 11C11.4 11 11 11.4 11 12V17.4L9.7 16.1C9.3 15.7 8.7 15.7 8.3 16.1C7.9 16.5 7.9 17.1 8.3 17.5L11.3 20.5C11.5 20.7 11.7 20.8 12 20.8C12.3 20.8 12.5 20.7 12.7 20.5L15.7 17.5C16.1 17.1 16.1 16.5 15.7 16.1C15.3 15.7 14.7 15.7 14.3 16.1L13 17.4Z" fill="currentColor"></path>
                            </svg>
                        </span>
                        تصدير
                    </button>
                    <!--end::Export-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            
            <!--begin::Card body-->
            <div class="card-body pt-0">
                <!--begin::Table-->
                <table class="table align-middle table-row-dashed fs-6 gy-5" id="orders-table">
                    <thead>
                        <tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
                            <th class="min-w-100px">رقم الطلب</th>
                            <th class="min-w-150px">العميل</th>
                            <th class="min-w-100px">الهاتف</th>
                            <th class="min-w-100px">المندوب</th>
                            <th class="min-w-100px">الحالة</th>
                            <th class="min-w-100px">المبلغ</th>
                            <th class="min-w-125px">تاريخ الإنشاء</th>
                            <th class="text-end min-w-70px">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody class="fw-semibold text-gray-600">
                        <!-- Data will be loaded via AJAX -->
                    </tbody>
                </table>
                <!--end::Table-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Card-->
    </div>
    <!--end::Content container-->
</div>
<!--end::Content-->
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Initialize DataTable with server-side processing
    var table = $('#orders-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: '{{ route("orders.ajax-data") }}',
            data: function(d) {
                d.status = $('#status-filter').val();
                d.date_range = $('#date-filter').val();
            }
        },
        columns: [
            { data: 'order_num', name: 'order_num' },
            { data: 'user_name', name: 'user.name' },
            { data: 'user_phone', name: 'user.phone' },
            { data: 'delegate_name', name: 'delegate.name' },
            { 
                data: 'status', 
                name: 'status',
                render: function(data) {
                    var statusMap = {
                        'pending': '<span class="badge badge-warning">معلق</span>',
                        'preparing': '<span class="badge badge-info">قيد التحضير</span>',
                        'done': '<span class="badge badge-success">مكتمل</span>',
                        'cancelled': '<span class="badge badge-danger">ملغي</span>'
                    };
                    return statusMap[data] || data;
                }
            },
            { data: 'total', name: 'total' },
            { data: 'created_at', name: 'created_at' },
            { data: 'actions', name: 'actions', orderable: false, searchable: false }
        ],
        order: [[6, 'desc']], // Order by created_at desc
        pageLength: 25,
        language: {
            url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/ar.json'
        },
        dom: 'rt<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        drawCallback: function() {
            // Re-initialize any tooltips or other UI elements
            $('[data-bs-toggle="tooltip"]').tooltip();
        }
    });

    // Search functionality
    $('#orders-search').on('keyup', function() {
        table.search(this.value).draw();
    });

    // Status filter
    $('#status-filter').on('change', function() {
        table.draw();
    });

    // Date range filter
    $('#date-filter').daterangepicker({
        autoUpdateInput: false,
        locale: {
            cancelLabel: 'مسح',
            applyLabel: 'تطبيق'
        }
    });

    $('#date-filter').on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' to ' + picker.endDate.format('YYYY-MM-DD'));
        table.draw();
    });

    $('#date-filter').on('cancel.daterangepicker', function(ev, picker) {
        $(this).val('');
        table.draw();
    });

    // Export functionality
    $('#export-orders').on('click', function() {
        // Implement export functionality
        window.location.href = '{{ route("orders.export") }}?' + $.param({
            status: $('#status-filter').val(),
            date_range: $('#date-filter').val(),
            search: $('#orders-search').val()
        });
    });
});
</script>
@endpush

@push('styles')
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
@endpush
