@extends('dashboard.layouts.app')
@section('pageTitle', __('dashboard.packages_overview'))

@section('content')

<!--begin::Post-->
<div class="post d-flex flex-column-fluid" id="kt_post">
    <!--begin::Container-->
    <div id="kt_content_container" class="container-xxl">
        
        <!--begin::Statistics Cards-->
        <div class="row g-5 g-xl-8 mb-8">
            <!--begin::Total Packages-->
            <div class="col-xl-3">
                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-xl-100" style="background-color: #F1416C;background-image:url('assets/media/patterns/vector-1.png')">
                    <div class="card-header pt-5">
                        <div class="card-title d-flex flex-column">
                            <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $totalPackages }}</span>
                            <span class="text-white opacity-75 pt-1 fw-bold fs-6">@lang('dashboard.total_packages')</span>
                        </div>
                    </div>
                    <div class="card-body d-flex align-items-end pt-0">
                        <div class="d-flex align-items-center flex-column mt-3 w-100">
                            <div class="d-flex justify-content-between w-100 mt-auto mb-2">
                                <span class="fw-bold fs-6 text-white opacity-75">@lang('dashboard.active_packages')</span>
                                <span class="fw-bolder fs-6 text-white">{{ $activePackages }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Total Packages-->
            
            <!--begin::Total Subscriptions-->
            <div class="col-xl-3">
                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-xl-100" style="background-color: #7239EA;background-image:url('assets/media/patterns/vector-1.png')">
                    <div class="card-header pt-5">
                        <div class="card-title d-flex flex-column">
                            <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $totalSubscriptions }}</span>
                            <span class="text-white opacity-75 pt-1 fw-bold fs-6">@lang('dashboard.total_subscriptions')</span>
                        </div>
                    </div>
                    <div class="card-body d-flex align-items-end pt-0">
                        <div class="d-flex align-items-center flex-column mt-3 w-100">
                            <div class="d-flex justify-content-between w-100 mt-auto mb-2">
                                <span class="fw-bold fs-6 text-white opacity-75">@lang('dashboard.active_subscriptions')</span>
                                <span class="fw-bolder fs-6 text-white">{{ $activeSubscriptions }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Total Subscriptions-->
            
            <!--begin::Total Users-->
            <div class="col-xl-3">
                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-xl-100" style="background-color: #17C653;background-image:url('assets/media/patterns/vector-1.png')">
                    <div class="card-header pt-5">
                        <div class="card-title d-flex flex-column">
                            <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $totalUsers }}</span>
                            <span class="text-white opacity-75 pt-1 fw-bold fs-6">@lang('dashboard.subscribed_users')</span>
                        </div>
                    </div>
                    <div class="card-body d-flex align-items-end pt-0">
                        <div class="d-flex align-items-center flex-column mt-3 w-100">
                            <div class="d-flex justify-content-between w-100 mt-auto mb-2">
                                <span class="fw-bold fs-6 text-white opacity-75">@lang('dashboard.unique_users')</span>
                                <span class="fw-bolder fs-6 text-white">{{ $uniqueUsers }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Total Users-->
            
            <!--begin::Total Washes-->
            <div class="col-xl-3">
                <div class="card card-flush bgi-no-repeat bgi-size-contain bgi-position-x-end h-xl-100" style="background-color: #FFC700;background-image:url('assets/media/patterns/vector-1.png')">
                    <div class="card-header pt-5">
                        <div class="card-title d-flex flex-column">
                            <span class="fs-2hx fw-bolder text-white me-2 lh-1 ls-n2">{{ $totalWashes }}</span>
                            <span class="text-white opacity-75 pt-1 fw-bold fs-6">@lang('dashboard.total_washes')</span>
                        </div>
                    </div>
                    <div class="card-body d-flex align-items-end pt-0">
                        <div class="d-flex align-items-center flex-column mt-3 w-100">
                            <div class="d-flex justify-content-between w-100 mt-auto mb-2">
                                <span class="fw-bold fs-6 text-white opacity-75">@lang('dashboard.remaining_washes')</span>
                                <span class="fw-bolder fs-6 text-white">{{ $remainingWashes }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--end::Total Washes-->
        </div>
        <!--end::Statistics Cards-->

        <!--begin::Packages Overview-->
        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                <!--begin::Card title-->
                <div class="card-title">
                    <h2 class="fw-bolder">@lang('dashboard.packages_overview')</h2>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <a href="{{ route('packages.index') }}" class="btn btn-primary">@lang('dashboard.manage_packages')</a>
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0">
                <!--begin::Table-->
                <table class="table align-middle table-row-dashed fs-6 gy-5">
                    <!--begin::Table head-->
                    <thead>
                        <tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
                            <th class="min-w-250px">@lang('dashboard.package')</th>
                            <th class="min-w-100px">@lang('dashboard.package_price')</th>
                            <th class="min-w-100px">@lang('dashboard.total_washes')</th>
                            <th class="min-w-120px">@lang('dashboard.subscriptions')</th>
                            <th class="min-w-120px">@lang('dashboard.active_subscriptions')</th>
                            <th class="min-w-120px">@lang('dashboard.total_revenue')</th>
                            <th class="min-w-100px">@lang('dashboard.actions')</th>
                        </tr>
                    </thead>
                    <!--end::Table head-->
                    <!--begin::Table body-->
                    <tbody class="fw-bold text-gray-600">
                        @forelse ($packages as $package)
                            @php
                                $subscriptionCount = $package->userpackages->count();
                                $activeSubscriptionCount = $package->userpackages->filter(function($sub) {
                                    return $sub->expire_at && \Carbon\Carbon::parse($sub->expire_at)->isFuture();
                                })->count();
                                $totalRevenue = $subscriptionCount * $package->price;
                            @endphp
                            <tr>
                                <!--begin::Package-->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="symbol symbol-50px me-3">
                                            <div class="symbol-label bg-light-primary text-primary fw-bolder">
                                                {{ substr($package->name, 0, 1) }}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="text-gray-800 fw-bolder fs-6">{{ $package->getTranslation('name', app()->getLocale()) }}</div>
                                            <div class="text-muted fs-7">{{ Str::limit($package->getTranslation('desc', app()->getLocale()), 50) }}</div>
                                        </div>
                                    </div>
                                </td>
                                <!--end::Package-->
                                
                                <!--begin::Price-->
                                <td>
                                    <div class="text-success fw-bolder fs-6">{{ $package->price }} @lang('dashboard.sar')</div>
                                </td>
                                <!--end::Price-->
                                
                                <!--begin::Washes-->
                                <td>
                                    <div class="badge badge-light-primary">{{ $package->washes }}</div>
                                </td>
                                <!--end::Washes-->
                                
                                <!--begin::Subscriptions-->
                                <td>
                                    <div class="text-gray-800 fw-bolder">{{ $subscriptionCount }}</div>
                                </td>
                                <!--end::Subscriptions-->
                                
                                <!--begin::Active Subscriptions-->
                                <td>
                                    <div class="badge badge-light-success">{{ $activeSubscriptionCount }}</div>
                                </td>
                                <!--end::Active Subscriptions-->
                                
                                <!--begin::Revenue-->
                                <td>
                                    <div class="text-success fw-bolder">{{ number_format($totalRevenue) }} @lang('dashboard.sar')</div>
                                </td>
                                <!--end::Revenue-->
                                
                                <!--begin::Actions-->
                                <td>
                                    <div class="d-flex gap-2">
                                        <a href="{{ route('packages.users', $package->id) }}" class="btn btn-sm btn-light-primary">
                                            @lang('dashboard.view_users')
                                        </a>
                                        <a href="{{ route('packages.edit', $package->id) }}" class="btn btn-sm btn-light-warning">
                                            @lang('dashboard.edit')
                                        </a>
                                    </div>
                                </td>
                                <!--end::Actions-->
                            </tr>
                        @empty
                            <tr>
                                <td colspan="7" class="text-center py-10">
                                    <div class="text-muted fs-6">@lang('dashboard.no_data_available')</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                    <!--end::Table body-->
                </table>
                <!--end::Table-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Packages Overview-->
    </div>
    <!--end::Container-->
</div>
<!--end::Post-->

@endsection
