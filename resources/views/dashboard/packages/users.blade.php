@extends('dashboard.layouts.app')
@section('pageTitle', __('dashboard.subscribed_users'))

@section('content')

<!--begin::Post-->
<div class="post d-flex flex-column-fluid" id="kt_post">
    <!--begin::Container-->
    <div id="kt_content_container" class="container-xxl">

        <!--begin::Package Info Card-->
        <div class="card card-flush mb-6">
            <div class="card-header">
                <div class="card-title">
                    <h2>@lang('dashboard.subscription_details')</h2>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="symbol symbol-50px me-3">
                                <span class="symbol-label" style="background-image:url({{ $package->image ?? '' }});"></span>
                            </div>
                            <div>
                                <div class="fw-bolder text-gray-800 fs-6">{{ $package ? $package->getTranslation('name', app()->getLocale()) : 'N/A' }}</div>
                                <div class="text-muted fs-7">{{ $package ? $package->getTranslation('desc', app()->getLocale()) : '' }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-muted fs-7">@lang('dashboard.package_price')</div>
                        <div class="fw-bolder text-success fs-6">{{ $package->price ?? 0 }} @lang('dashboard.sar')</div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-muted fs-7">@lang('dashboard.total_washes')</div>
                        <div class="fw-bolder text-primary fs-6">{{ $package->washes ?? 0 }}</div>
                    </div>
                    <div class="col-md-2">
                        <div class="text-muted fs-7">@lang('dashboard.subscription_duration')</div>
                        <div class="fw-bolder text-info fs-6">{{ $package->duration ?? 0 }}</div>
                    </div>
                    <div class="col-md-3">
                        <a href="{{ route('packages.index') }}" class="btn btn-secondary">@lang('dashboard.back')</a>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Package Info Card-->

        <!--begin::Subscriptions Table-->
        <div class="card card-flush">
            <!--begin::Card header-->
            <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                <!--begin::Card title-->
                <div class="card-title">
                    <h3 class="fw-bolder">@lang('dashboard.subscribed_users')</h3>
                </div>
                <!--end::Card title-->
                <!--begin::Card toolbar-->
                <div class="card-toolbar">
                    <!--begin::Search-->
                    <div class="d-flex align-items-center position-relative my-1">
                        <span class="svg-icon svg-icon-1 position-absolute ms-4">
                            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2" rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
                                <path d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z" fill="currentColor" />
                            </svg>
                        </span>
                        <input type="text" data-kt-ecommerce-category-filter="search" class="form-control form-control-solid w-250px ps-14" placeholder="@lang('dashboard.search')" />
                    </div>
                    <!--end::Search-->
                </div>
                <!--end::Card toolbar-->
            </div>
            <!--end::Card header-->
            <!--begin::Card body-->
            <div class="card-body pt-0">
                <!--begin::Table-->
                <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_category_table">
                    <!--begin::Table head-->
                    <thead>
                        <!--begin::Table row-->
                        <tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
                            <th class="min-w-200px">@lang('dashboard.user_name')</th>
                            <th class="min-w-150px">@lang('dashboard.user_phone')</th>
                            <th class="min-w-150px">@lang('dashboard.user_email')</th>
                            <th class="min-w-120px">@lang('dashboard.subscription_start_date')</th>
                            <th class="min-w-120px">@lang('dashboard.subscription_end_date')</th>
                            <th class="min-w-100px">@lang('dashboard.total_washes')</th>
                            <th class="min-w-100px">@lang('dashboard.used_washes')</th>
                            <th class="min-w-100px">@lang('dashboard.remaining_washes')</th>
                            <th class="min-w-100px">@lang('dashboard.subscription_status')</th>
                        </tr>
                        <!--end::Table row-->
                    </thead>
                    <!--end::Table head-->
                    <!--begin::Table body-->
                    <tbody class="fw-bold text-gray-600">
                        @forelse ($subscriptions as $subscription)
                            @php
                                $isActive = $subscription->expire_at && \Carbon\Carbon::parse($subscription->expire_at)->isFuture();
                                $usedWashes = $subscription->washes - $subscription->remain_washes;
                                $startDate = $subscription->created_at;
                                $endDate = $subscription->expire_at;
                            @endphp
                            <!--begin::Table row-->
                            <tr data-id="{{ $subscription->id }}">
                                <!--begin::User Info-->
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="symbol symbol-40px me-3">
                                            <div class="symbol-label bg-light-primary text-primary fw-bolder">
                                                {{ substr($subscription->user->name ?? 'U', 0, 1) }}
                                            </div>
                                        </div>
                                        <div>
                                            <div class="text-gray-800 fw-bolder fs-6">{{ $subscription->user->name ?? 'N/A' }}</div>
                                            <div class="text-muted fs-7">ID: {{ $subscription->user->id ?? 'N/A' }}</div>
                                        </div>
                                    </div>
                                </td>
                                <!--end::User Info-->

                                <!--begin::Phone-->
                                <td>
                                    <div class="text-gray-800 fw-bolder">{{ $subscription->user->phone ?? 'N/A' }}</div>
                                </td>
                                <!--end::Phone-->

                                <!--begin::Email-->
                                <td>
                                    <div class="text-gray-800">{{ $subscription->user->email ?? 'N/A' }}</div>
                                </td>
                                <!--end::Email-->

                                <!--begin::Start Date-->
                                <td>
                                    <div class="text-gray-800">
                                        {{ $startDate ? $startDate->format('Y-m-d') : 'N/A' }}
                                    </div>
                                    <div class="text-muted fs-7">
                                        {{ $startDate ? $startDate->format('H:i') : '' }}
                                    </div>
                                </td>
                                <!--end::Start Date-->

                                <!--begin::End Date-->
                                <td>
                                    <div class="text-gray-800">
                                        {{ $endDate ? \Carbon\Carbon::parse($endDate)->format('Y-m-d') : 'N/A' }}
                                    </div>
                                    <div class="text-muted fs-7">
                                        @if($endDate)
                                            {{ \Carbon\Carbon::parse($endDate)->diffForHumans() }}
                                        @endif
                                    </div>
                                </td>
                                <!--end::End Date-->

                                <!--begin::Total Washes-->
                                <td>
                                    <div class="badge badge-light-primary fs-7">{{ $subscription->washes ?? 0 }}</div>
                                </td>
                                <!--end::Total Washes-->

                                <!--begin::Used Washes-->
                                <td>
                                    <div class="badge badge-light-warning fs-7">{{ $usedWashes }}</div>
                                </td>
                                <!--end::Used Washes-->

                                <!--begin::Remaining Washes-->
                                <td>
                                    <div class="badge badge-light-success fs-7">{{ $subscription->remain_washes ?? 0 }}</div>
                                </td>
                                <!--end::Remaining Washes-->

                                <!--begin::Status-->
                                <td>
                                    @if($isActive)
                                        <div class="badge badge-light-success">@lang('dashboard.subscription_active')</div>
                                    @else
                                        <div class="badge badge-light-danger">@lang('dashboard.subscription_expired')</div>
                                    @endif
                                </td>
                                <!--end::Status-->
                            </tr>
                            <!--end::Table row-->
                        @empty
                            <tr>
                                <td colspan="9" class="text-center py-10">
                                    <div class="text-muted fs-6">@lang('dashboard.no_data_available')</div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                    <!--end::Table body-->
                </table>
                <!--end::Table-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::Subscriptions Table-->
    </div>
    <!--end::Container-->
</div>
<!--end::Post-->

@endsection