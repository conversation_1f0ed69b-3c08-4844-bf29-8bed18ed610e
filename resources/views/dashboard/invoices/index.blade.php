@extends('dashboard.layouts.app')
@section('pageTitle', __('dashboard.invoices'))

@section('content')
    <!--begin::Post-->
    <div class="post d-flex flex-column-fluid" id="kt_post">
        <!--begin::Container-->
        <div id="kt_content_container" class="container-xxl">
            <!--begin::Invoices-->
            <div class="card card-flush">
                <!--begin::Card header-->
                <div class="card-header align-items-center py-5 gap-2 gap-md-5">
                    <div class="card-title">
                        <!-- Search -->
                        <div class="d-flex align-items-center position-relative my-1">
                            <span class="svg-icon svg-icon-1 position-absolute ms-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none">
                                    <rect opacity="0.5" x="17.0365" y="15.1223" width="8.15546" height="2"
                                        rx="1" transform="rotate(45 17.0365 15.1223)" fill="currentColor" />
                                    <path
                                        d="M11 19C6.55556 19 3 15.4444 3 11C3 6.55556 6.55556 3 11 3C15.4444 3 19 6.55556 19 11C19 15.4444 15.4444 19 11 19ZM11 5C7.53333 5 5 7.53333 5 11C5 14.4667 7.53333 17 11 17C14.4667 17 17 14.4667 17 11C17 7.53333 14.4667 5 11 5Z"
                                        fill="currentColor" />
                                </svg>
                            </span>
                            <input type="text" data-kt-ecommerce-category-filter="search"
                                class="form-control form-control-solid w-250px ps-14" placeholder="@lang('dashboard.search_title', ['page_title' => __('dashboard.invoices')])" />
                        </div>
                    </div>
                    <div class="card-toolbar">
                        <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                            @lang('dashboard.create_title', ['page_title' => __('dashboard.invoices')])
                        </a>
                    </div>
                </div>
                <!--end::Card header-->
                <!--begin::Card body-->
                <div class="card-body pt-0">
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_ecommerce_category_table">
                        <thead>
                            <tr class="text-start text-gray-400 fw-bolder fs-7 text-uppercase gs-0">
                                <th>#</th>
                                <th>@lang('dashboard.user_name')</th>
                                <th>@lang('dashboard.invoice_date')</th>
                                <th>@lang('dashboard.created_at')</th>
                                <th>@lang('dashboard.payment_url')</th>
                                <th>@lang('dashboard.payment_status')</th>

                                <th>@lang('dashboard.total')</th>
                                <th>@lang('dashboard.services_count')</th>
                                <th class="text-end">@lang('dashboard.actions')</th>
                            </tr>
                        </thead>
                        <tbody class="fw-bold text-gray-600">
                            @forelse ($invoices as $invoice)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $invoice->user->name ?? '-' }}</td>
                                    <td>{{ $invoice->invoice_date ?? '-' }}</td>
                                    <td>{{ $invoice->created_at ? $invoice->created_at->format('Y-m-d H:i') : '-' }}</td>
                                    <td>
                                        @if($invoice->payment_url)
                                            <a href="{{ $invoice->payment_url }}" target="_blank" id="paymentUrl">
                                              رابط الدفع
                                            </a>
                                        @else
                                            <span class="text-muted">-</span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($invoice->payment_status)
                                            @lang('dashboard.payment_status_' . strtolower($invoice->payment_status))
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td>{{ number_format($invoice->total ?? 0, 2) }}</td>
                                    <td>{{ $invoice->services ? $invoice->services->count() : 0 }}</td>
                                    <td class="text-end">
                                        <a href="{{ route('invoices.edit', $invoice->id) }}" class="btn btn-sm btn-light btn-active-light-primary">
                                            @lang('dashboard.edit')
                                        </a>
                                        <a href="{{ route('invoices.show', $invoice->id) }}" class="btn btn-sm btn-success btn-active-light-primary" target="_blank">
                                            @lang('dashboard.show')
                                            <span>{{ trans('dashboard.type_label', ['type' => __('dashboard.' . ($invoice->type ?? 'invoice'))]) }}</span>
                                        </a>

                                        <button type="button" class="btn btn-sm btn-warning paid-cash-btn"
                                            data-id="{{ $invoice->id }}" data-url="{{ route('invoices.paidCash', $invoice->id) }}">
                                            @lang('dashboard.paid_cash')
                                        </button>

                                        <button type="button" class="btn btn-sm btn-danger delete-invoice-btn"
                                            data-id="{{ $invoice->id }}" data-url="{{ route('invoices.destroy', $invoice->id) }}">
                                            @lang('dashboard.delete')
                                        </button>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="8" class="text-center py-5">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-file-invoice fa-3x text-muted mb-3"></i>
                                            <h5 class="text-muted">@lang('dashboard.no_data_found')</h5>
                                            <p class="text-muted">@lang('dashboard.no_invoices_message')</p>
                                            <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                                                @lang('dashboard.create_title', ['page_title' => __('dashboard.invoice')])
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                <!--end::Card body-->
            </div>
            <!--end::Invoices-->
        </div>
        <!--end::Container-->
    </div>
    <!--end::Post-->
@endsection

@section('scripts')
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        // Handle delete button click
        $(document).on('click', '.delete-invoice-btn', function () {
            const button = $(this);
            const invoiceId = button.data('id');
            const deleteUrl = button.data('url');

            // Show confirmation popup
            Swal.fire({
                title: "@lang('dashboard.confirm_delete_title')",
                text: "@lang('dashboard.confirm_delete_message')",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#d33",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "@lang('dashboard.confirm_delete')",
                cancelButtonText: "@lang('dashboard.cancel')",
            }).then((result) => {
                if (result.isConfirmed) {
                    // Perform delete action via AJAX
                    $.ajax({
                        url: deleteUrl,
                        type: "POST",
                        data: {
                            _token: "{{ csrf_token() }}",
                            _method: "DELETE",
                        },
                        success: function (response) {
                            // Show success popup with JSON response
                            Swal.fire({
                                title: "@lang('dashboard.delete_success_title')",
                                text: response.message,
                                icon: "success",
                                timer: 3000,
                            }).then(() => {
                                location.reload(); // Reload the page
                            });
                        },
                        error: function (xhr) {
                            // Show error popup
                            Swal.fire({
                                title: "@lang('dashboard.delete_error_title')",
                                text: xhr.responseJSON.message || "@lang('dashboard.something_went_wrong')",
                                icon: "error",
                            });
                        },
                    });
                }
            });
        });
    </script>
    <script>
        function copyOnDoubleClick(element) {
            // Get the text content of the clicked element
            const paymentUrl = element.innerText;

            // Copy the text to the clipboard
            navigator.clipboard.writeText(paymentUrl).then(() => {
                // Change the text color to light green
                element.style.color = '#28a745';
                const confirmation = document.createElement('span');
        confirmation.innerText = 'Copied!';
        confirmation.style.color = '#28a745';
        confirmation.style.marginLeft = '10px';
        confirmation.style.fontSize = '0.9em';

        element.parentNode.appendChild(confirmation);

                // Reset the color back to original after 1 second
                setTimeout(() => {
                    element.style.color = '#007bff'; // Original color
                    confirmation.remove();
                }, 1000);
            }).catch(err => {
                console.error('Error copying link:', err);
            });
        }
    </script>
    <script>
        // Handle "Paid Cash" button click
        $(document).on('click', '.paid-cash-btn', function () {
            const button = $(this);
            const invoiceId = button.data('id');
            const paidCashUrl = button.data('url');

            // Show confirmation popup
            Swal.fire({
                title: "@lang('dashboard.confirm_paid_cash_title')",
                text: "@lang('dashboard.confirm_paid_cash_message')",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#28a745",
                cancelButtonColor: "#d33",
                confirmButtonText: "@lang('dashboard.confirm_paid')",
                cancelButtonText: "@lang('dashboard.cancel')",
            }).then((result) => {
                if (result.isConfirmed) {
                    // Perform paidCash action via AJAX
                    $.ajax({
                        url: paidCashUrl,
                        type: "PUT",
                        data: {
                            _token: "{{ csrf_token() }}",
                        },
                        success: function (response) {
                            // Show success popup with JSON response
                            Swal.fire({
                                title: "@lang('dashboard.paid_success_title')",
                                text: response.message,
                                icon: "success",
                                timer: 3000,
                            }).then(() => {
                                location.reload(); // Reload the page
                            });
                        },
                        error: function (xhr) {
                            // Show error popup
                            Swal.fire({
                                title: "@lang('dashboard.paid_error_title')",
                                text: xhr.responseJSON.message || "@lang('dashboard.something_went_wrong')",
                                icon: "error",
                            });
                        },
                    });
                }
            });
        });
    </script>

@endsection
