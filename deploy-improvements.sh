#!/bin/bash

# 🎯 QatraWahda Package Management Improvements Deployment
# This script deploys ONLY the package management improvements we made

set -e

# Configuration - UPDATE THESE VALUES
VPS_USER="your_username"
VPS_HOST="your_vps_ip_or_domain"
VPS_PATH="/path/to/your/production/project"

echo "🎯 Deploying Package Management Improvements..."

# Create timestamp for backups
timestamp=$(date +%Y%m%d_%H%M%S)

echo "📦 Creating backup of files to be updated..."

# Backup current production files before updating
ssh $VPS_USER@$VPS_HOST << EOF
    cd $VPS_PATH
    mkdir -p ./deployment_backups
    
    # Backup files we're about to change
    cp app/Http/Controllers/Dashboard/PackageController.php ./deployment_backups/PackageController_backup_$timestamp.php
    cp -r resources/views/dashboard/packages ./deployment_backups/packages_views_backup_$timestamp
    cp -r lang ./deployment_backups/lang_backup_$timestamp
    cp routes/web.php ./deployment_backups/web_routes_backup_$timestamp.php
    
    echo "✅ Production files backed up"
EOF

echo "📤 Uploading improved files..."

# Upload the specific files we improved
echo "  → Uploading PackageController..."
scp ./app/Http/Controllers/Dashboard/PackageController.php \
    $VPS_USER@$VPS_HOST:$VPS_PATH/app/Http/Controllers/Dashboard/

echo "  → Uploading package views..."
scp -r ./resources/views/dashboard/packages/ \
    $VPS_USER@$VPS_HOST:$VPS_PATH/resources/views/dashboard/

echo "  → Uploading language files..."
scp -r ./lang/ \
    $VPS_USER@$VPS_HOST:$VPS_PATH/

echo "  → Uploading routes..."
scp ./routes/web.php \
    $VPS_USER@$VPS_HOST:$VPS_PATH/routes/

echo "✅ All improved files uploaded"

echo "🔧 Applying production optimizations..."

ssh $VPS_USER@$VPS_HOST << EOF
    cd $VPS_PATH
    
    echo "Clearing caches..."
    php artisan config:clear
    php artisan route:clear
    php artisan view:clear
    php artisan cache:clear
    
    echo "Rebuilding caches for production..."
    php artisan config:cache
    php artisan route:cache
    php artisan view:cache
    
    echo "Setting proper permissions..."
    chown -R www-data:www-data resources/views/dashboard/packages
    chown -R www-data:www-data lang
    chown www-data:www-data app/Http/Controllers/Dashboard/PackageController.php
    chown www-data:www-data routes/web.php
    
    echo "✅ Production optimizations applied"
EOF

echo "🧪 Testing deployment..."

ssh $VPS_USER@$VPS_HOST << EOF
    cd $VPS_PATH
    
    echo "Testing package model and localization..."
    php artisan tinker --execute="
        try {
            echo 'Testing Package Model...' . PHP_EOL;
            \$packages = App\Models\Package::with('userpackages')->get();
            echo 'Packages found: ' . \$packages->count() . PHP_EOL;
            
            foreach(\$packages as \$package) {
                echo 'Package ID: ' . \$package->id . PHP_EOL;
                echo '  Arabic: ' . \$package->getTranslation('name', 'ar') . PHP_EOL;
                echo '  English: ' . \$package->getTranslation('name', 'en') . PHP_EOL;
                echo '  Subscriptions: ' . \$package->userpackages->count() . PHP_EOL;
                echo '---' . PHP_EOL;
            }
            
            echo 'Current locale: ' . app()->getLocale() . PHP_EOL;
            echo '✅ All tests passed!' . PHP_EOL;
            
        } catch (Exception \$e) {
            echo '❌ Error: ' . \$e->getMessage() . PHP_EOL;
            echo 'File: ' . \$e->getFile() . ':' . \$e->getLine() . PHP_EOL;
        }
    "
EOF

echo ""
echo "🎉 Package Management Improvements Deployed Successfully!"
echo ""
echo "📋 What was deployed:"
echo "  ✅ Enhanced PackageController with proper localization"
echo "  ✅ Improved package index view with bilingual names"
echo "  ✅ New package overview page with statistics"
echo "  ✅ Enhanced package users view with subscription details"
echo "  ✅ Updated language files with new translations"
echo "  ✅ New route for package overview"
echo ""
echo "🔗 Test your improvements:"
echo "  • Dashboard: https://your-domain.com/dashboard"
echo "  • Packages: https://your-domain.com/packages"
echo "  • Package Overview: https://your-domain.com/packages/overview"
echo ""
echo "🚨 If you need to rollback:"
echo "  ssh $VPS_USER@$VPS_HOST"
echo "  cd $VPS_PATH"
echo "  cp ./deployment_backups/PackageController_backup_$timestamp.php app/Http/Controllers/Dashboard/PackageController.php"
echo "  cp -r ./deployment_backups/packages_views_backup_$timestamp/* resources/views/dashboard/packages/"
echo "  cp -r ./deployment_backups/lang_backup_$timestamp/* lang/"
echo "  cp ./deployment_backups/web_routes_backup_$timestamp.php routes/web.php"
echo "  php artisan cache:clear && php artisan config:cache"
