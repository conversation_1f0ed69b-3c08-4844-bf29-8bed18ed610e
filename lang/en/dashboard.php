<?php

return [
    'app_name' => 'Qatra Wahda',
    'app_desc' => 'Qatra Wahda',
    'app_key_words' => 'Qatra Wahda , website , application',
    'dashboard' => 'Dashboard',
    'home' => 'Home',
    'back_to_home' => 'Back Home',
    'general' => 'General',
    'AR' => 'Arabic Data',
    'EN' => 'English Data',
    'arabic_data' => 'Data in Arabic',
    'english_data' => 'Data in English',
    'show_title' => 'Show :page_title',
    'status_title' => 'Status :page_title',
    'status_desc' => 'Set :page_title Status',
    'create_title' => 'Add :page_title',
    'search_title' => 'Search :page_title',
    'update_title' => 'Edit :page_title Data',
    'index' => 'Use this page to View, Edit, or Delete :page_title.',
    'create' => 'Fill the input fields below to create :page_title.',
    'update' => 'Edit the input fields below to update :page_title data.',
    'index_delete' => 'Use this page to View or Delete :page_title.',
    'show' => 'Use this page to View Details of :page_title.',
    'all_title' => 'Add :page_title',
    'latest_orders' => 'Latest Orders',
    'add_user' => 'Add user',
    'enter_user_phone' => 'enter user phone',
    'categories' => 'Wash Types',
    'category' => 'Wash Type',
    'cars' => 'Car Types',
    'car' => 'Car Type',
    'packages' => 'Packages',
    'package' => 'Package',
    'banners' => 'Banners',
    'banner' => 'Banner',
    'cities' => 'Cities',
    'city' => 'City',
    'targets' => 'Targets',
    'target' => 'Target',
    'add_new' => 'Add New',
    'users' => 'Users',
    'user' => 'User',
    'notifications' => 'Notifications',
    'settings' => 'Settings',
    'is_percentage' => 'Is Percentage Coupon ?',
    'expired_at' => 'Coupon Expired at',
    'user_times' => 'User Times of coupon',
    'times' => 'Times of uses',
    'code_coupon' => 'Coupon Code',
    'percentage' => 'Percentage',
    'discount' => 'Discount',
    'wash_type' => 'Wash Type',
    'not_active' => 'DisActive',
    'active' => 'Active',
    'coupons' => 'Coupons',
    'coupon' => 'Coupon',
    'application_name_ar' => 'Application Name In Arabic',
    'application_name_en' => 'Application Name In English',
    'about_en' => 'About Application (EN)',
    'about_ar' => 'About Application (AR)',
    'privacy_en' => 'Privacy (EN)',
    'privacy_ar' => 'Privacy (AR)',
    'terms_en' => 'Terms and Conditions (EN)',
    'terms_ar' => 'Terms and Conditions (AR)',
    'static_pages' => 'Static Pages Settings',
    'face_book' => 'Facebook',
    'twitter' => 'Twitter',
    'instagram' => 'Instagram',
    'phone' => 'Phone',
    'email' => 'E-mail',
    'address' => 'Addresse',
    'firebase_key' => 'Firebase Key',
    'status' => 'Activation Status',
    'sender_name' => 'Sender Name',
    'user_name' => 'User Name',
    'password' => 'Password',
    'name' => 'Name',
    'description' => 'Description',
    'orders' => 'Orders',
    'pending_orders' => 'Pending Orders',
    'preparing_orders' => 'Preparing Orders',
    'done_orders' => 'Completed Orders',
    'cancelled_orders' => 'Cancelled Orders',
    'user_order' => 'Order',
    'sms_providers' => 'SMS providers',
    'RS' => 'RS',
    'notification_body_ar' => 'Notification message (AR)',
    'notification_body_en' => 'Notification message (EN)',
    'notification_title_ar' => 'Notification Title (AR)',
    'notification_title_en' => 'Notification Title (EN)',
    'send_notification' => 'Send Notification',
    'show_orders_of' => 'Show orders of',
    'actions' => 'Actions',
    'actionsBtn' => 'Actions',
    'accept_as_delegate' => 'Upgrade to Delegate',
    'cond_delegate' => 'If you need more info, please check',
    'delegate_requirments' => 'Delegate Attachemts',
    'identity_image' => 'Identity Image',
    'car_license' => 'Car License',
    'front_car_image' => 'Front Car Image',
    'back_car_image' => 'Back Car Image',
    'car_image' => 'Car Image',
    'admins' => 'Admins',
    'admin' => 'Admin',
    'permissions' => 'Permissions',
    'permissions_of' => 'Permissions of',
    'settings' => 'Settings',
    'logs' => 'Logs',

    'image' => 'Image',
    'image_requirments' => 'Set image. Only *.png, *.jpg and *.jpeg image files are accepted',

    'cancel' => 'Cancel',
    'save_changes' => 'Save Changes',
    'please_wait' => 'Please wait...',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'delete_selected' => 'Delete Selected',
    'choose_image' => 'Choose image',
    'language' => 'Language',
    'page_order' => 'Page Order ',
    'order' => 'Order',
    'initial_page' => 'Initial Page',
    'initial_pages' => 'Initial Pages',
    'allow_notifications' => 'Allow Notifications',
    'notification' => 'Notification',
    'title_admin_notify' => 'Admin Notification',
    'sms' => 'SMS',
    'notification_message_ar' => 'Message (AR)',
    'notification_message_en' => 'Message (EN)',
    'notification_data' => 'Notification Details',
    'send_to' => 'Send To ..',
    'full_name' => 'Full Name',
    'first_name' => 'First Name',
    'last_name' => 'Last Name',
    'password_confirmation' => 'Password Confirmation',
    'role' => 'Role',
    'select_role' => 'Select a Role...',
    'select_language' => 'Select Language',
    'sign_in_to_dashboard' => 'Sign In to Dashboard',
    'profile_details' => 'Profile Details',
    'deactivate_account' => 'Deactivate Account',
    'deactivate_desc' => '<h4 class="text-gray-900 fw-bolder">You Are Deactivating Account</h4>
    <div class="fs-6 text-gray-700">For extra security, this requires you to confirm  email or phone number when you reset yousignr password.',

    'confirm_deactivate' => 'I confirm account deactivation',

    'activate_account' => 'Activate Account',
    'activate_desc' => '<h4 class="text-gray-900 fw-bolder">You Are Activating Account</h4>
    <div class="fs-6 text-gray-700"> When Activate account user can login anytime',

    'confirm_activate' => 'I confirm account activation',
    'activation_status' => 'Change Accounts Activation Status',
    'account_status' => 'Account Status',
    'nickname_ar' => 'Role Name (AR)',
    'nickname_en' => 'Role Name (EN)',
    'select_all_permissions' => 'Select All Permissions',
    'select_all' => 'Select All',
    'user_attachments' => 'User Attachments',
    'update_settings' => 'Update Settings',
    'set_lang' => 'Change Language',
    'update_sms' => 'Update SMS providers',
    'send_one_notification' => 'Send Single Notification',
    'send_group_notification' => 'Send Group Notification',
    'enable_delegate_register' => 'Enable Delegates Register',
    "enable_package" => "Enable Package",
    'yes' => 'Yes',
    'no' => 'No',
    'app_percentage' => 'Application Percentage',
    'delegates_and_orders' => 'Delegates and Orders',
    'orders_count' => 'Orders Count',
    'total' => 'Total',
    'earnings' => 'Earnings',
    'raters_count' => 'Number of reviews',
    'top_rated_providers' => 'Top Rated Providers',

    "Jan" => ".Jan",
    "Feb" => ".Feb",
    "Mar" => ".Mar",
    "Apr" => ".Apr",
    "May" => ".May",
    "Jun" => ".Jun",
    "Jul" => ".Jul",
    "Aug" => ".Aug",
    "Sept" => ".Sept",
    "Oct" => ".Oct",
    "Nov" => ".Nov",
    "Dec" => ".Dec",
    "most_ordered" => "Most Ordered",
    "most_ordered_categories" => "Most Ordered Categories",
    "view_all" => "View all",
    "active_users" => "Active Users",
    "providers" => "Drivers",
    "all_users" => "All Users",
    "external_link" => "External Link",
    'category_or_link' => 'Category Or Link',
    "not_selected_yet" => "Not Selected Yet!",
    "assign_order" => "Assign Order",
    "delegates" => "Delegates",
    "delegate" => "Delegate",
    "select_delegate" => "Select Delegate",
    'states' => 'States',
    'state' => 'State',
    'account_settings' => 'Account Settings',
    'sign_out' => 'Sign Out',
    'title_order' => 'Change Order Status',
    'title_new-order' => 'New Order',
    'washes' => 'Washes',
    'wash' => 'Wash',
    'remain_washes' => 'Remain Washes',
    'brands' => 'Brands',
    'brand' => 'Brand',
    'name_desc' => 'A :page_title name is required and recommended to be unique.',
    'description' => 'Description',
    'desc_requirments' => 'Set a description to the :page_title for better visibility.',
    'price_desc' => 'Set a :page_title Only numbers are accepted greater than zero',
    'price' => 'Price',
    'product' => 'Product',
    'products' => 'Products',
    'select_brand' => 'Select Brand',
    'quantity' => 'Quantity',
    'prices' => 'Prices',
    'select_car' => 'Select Car',
    'select_category' => 'Select Wash Type',
    'control_panel' => 'Control Panel',
    'dashboard' => 'Dashboard',
    'pages' => 'Pages',
    'duration_to' => 'Duration To',
    'duration_from' => 'Duration From',
    'duration' => 'Duration',
    'market' => 'Market',
    'policy' => 'Policy',
    'terms' => 'Terms and Conditions',
    'login' => 'Login',
    'enter_phone' => 'Enter Phone Number',
    'complete_data' => 'Complete Data',
    'subscribe' => 'Subscribe',
    'the_state' => 'State',
    'work' => 'Work',
    'work_type' => 'Work Type',
    'works' => 'Works',
    'video_link' => 'Video Link',
    'our_works' => 'Our Works',
    'car_type' => 'Car Type',
    'category_type' => 'Category Type',
    'location' => 'Location',
    'address' => 'Address',
    'country' => 'Country',
    'location_detailed' => 'Location Detailed',
    'map_placeholder' => 'Type Your Address here or choose from map',
    'order_details' => 'Order Details',
    'created_at' => 'Creation Date',

    // Payment Status Translations
    'payment_status_waiting' => 'Waiting',
    'payment_status_paid' => 'Paid',
    'payment_status_paid_cash' => 'Paid Cash',
    'payment_status_pending' => 'Pending',
    'payment_status_failed' => 'Failed',
    'payment_status_cancelled' => 'Cancelled',
    'payment_status_expired' => 'Expired',

    // Package Subscription Translations
    'subscription_details' => 'Subscription Details',
    'subscribed_users' => 'Subscribed Users',
    'subscription_start_date' => 'Start Date',
    'subscription_end_date' => 'End Date',
    'subscription_status' => 'Status',
    'total_washes' => 'Total Washes',
    'used_washes' => 'Used Washes',
    'remaining_washes' => 'Remaining Washes',
    'subscription_active' => 'Active',
    'subscription_expired' => 'Expired',
    'subscription_inactive' => 'Inactive',
    'user_phone' => 'Phone',
    'user_email' => 'Email',
    'package_price' => 'Package Price',
    'subscription_duration' => 'Duration (Days)',
    'sar' => 'SAR',
    'back' => 'Back',
    'search' => 'Search',
    'no_data_available' => 'No data available',
    'subscriptions' => 'Subscriptions',
    'packages_overview' => 'Packages Overview',
    'total_packages' => 'Total Packages',
    'active_packages' => 'Active Packages',
    'total_subscriptions' => 'Total Subscriptions',
    'active_subscriptions' => 'Active Subscriptions',
    'unique_users' => 'Unique Users',
    'total_revenue' => 'Total Revenue',
    'manage_packages' => 'Manage Packages',
    'view_users' => 'View Users',
    'selected_location' => 'Saved Location',
    'aim'  => 'Aim',
    'aims' => 'Aims',
    'edit_profile' => 'Edit Profile',
    'my_profile'   => 'My Profile',
    'scheduled_at'   => 'Scheduled At',
    'service_data'   => 'Service Data',
    'offer_details'   => 'Offer Details',
    'offer_cost'   => 'Offer Cost',
    'offers'     => 'Offers',
    'offer'      => 'Offer',
    'price_before_discount' => 'Price Before Discount',
    'price_after_discount' => 'Price After Discount',
    'pay_offer' => 'Pay Offer',

    'withdraws_status'    => [
        'status' => [
            'pending' => 'Pending',
            'approved' => 'Approved',
            'rejected' => 'Rejected',
            'cancelled' => 'Cancelled',
            'processing' => 'Processing',
            'completed' => 'Completed',
        ]
    ],

    'withdraw' => 'Withdraw',
    'withdraws' => 'Withdraws',
    'amount' => 'Amount',
    'iban' => 'IBAN',
    'bank_name' => 'Bank Name',
    'account_number' => 'Account Number',
    'withdraw_details' => 'Withdraw Details',
    'withdraw_num' => 'Withdraw Number',
    'user_details' => 'User Details',
    'type'                              => 'Type',
    'gateway'                           => 'Gateway',
    'online_payment'                    => 'Online Payment',
    'transaction_status'                => 'Transaction Status',
    'order_num'                         => 'Order Num',
    'transactions'       => 'Transactions',
    'transaction'        => 'Transaction',


    'transaction_type' => [
        '1' => 'Payment',
        '2' => 'Refund',
        '3' => 'Withdraw',
        '4' => 'Deposit',
    ],

    'transaction_gateway' => [
        '1' => 'visa',
        '2' => 'Mastercard',
        '3' => 'Mada',
        '4' => 'Google pay',
        '5' => 'Apple pay',
    ],

    'transaction_status' => [
        'successful' => 'Success',
        'fail' => 'Fail',
    ],

    'cart' => 'Cart',
    'create_working_hour' => 'Create Working Hour',
    'hour' => 'Hour',
    'capacity' => 'Capacity',
    'cancel' => 'Cancel',
    'create' => 'Create',
    'working_hours_list' => 'Working Hours List',
    'success_create' => 'Working hour created successfully!',
    'error_occurred' => 'Something went wrong!',
];
